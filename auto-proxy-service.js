/**
 * 自动代理服务启动器
 * 在后台自动启动和管理代理服务器
 */

const express = require('express')
const cors = require('cors')
const axios = require('axios')
const { spawn } = require('child_process')
const path = require('path')
const fs = require('fs')

class AutoProxyService {
  constructor() {
    this.app = express()
    this.PORT = 3001
    this.isRunning = false
    this.proxyProcess = null
    this.setupMiddleware()
    this.setupRoutes()
  }

  /**
   * 设置中间件
   */
  setupMiddleware() {
    this.app.use(cors({
      origin: '*',
      methods: ['GET', 'POST', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization']
    }))
    this.app.use(express.json())
    this.app.use(express.static('public'))
  }

  /**
   * 设置路由
   */
  setupRoutes() {
    // 健康检查
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        port: this.PORT,
        service: 'auto-proxy-service'
      })
    })

    // 代理路由 - 获取页面内容
    this.app.post('/proxy/fetch-page', async (req, res) => {
      try {
        const { url, headers = {} } = req.body
        
        if (!url) {
          return res.status(400).json({
            success: false,
            error: '缺少URL参数'
          })
        }

        console.log(`📡 代理请求: ${url}`)

        // 发送请求
        const response = await axios.get(url, {
          headers: {
            'User-Agent': headers['User-Agent'] || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            ...headers
          },
          timeout: 30000,
          maxRedirects: 5
        })

        res.json({
          success: true,
          html: response.data,
          status: response.status,
          headers: response.headers
        })

      } catch (error) {
        console.error('❌ 代理请求失败:', error.message)
        
        res.status(500).json({
          success: false,
          error: error.message,
          details: error.response ? {
            status: error.response.status,
            statusText: error.response.statusText
          } : null
        })
      }
    })

    // 自动启动接口
    this.app.post('/auto-start', async (req, res) => {
      try {
        const result = await this.autoStart()
        res.json(result)
      } catch (error) {
        res.status(500).json({
          success: false,
          error: error.message
        })
      }
    })

    // 状态查询接口
    this.app.get('/status', (req, res) => {
      res.json({
        isRunning: this.isRunning,
        port: this.PORT,
        processId: this.proxyProcess ? this.proxyProcess.pid : null,
        uptime: this.isRunning ? process.uptime() : 0
      })
    })
  }

  /**
   * 检查端口是否被占用
   */
  async checkPortInUse(port) {
    return new Promise((resolve) => {
      const net = require('net')
      const server = net.createServer()
      
      server.listen(port, () => {
        server.once('close', () => {
          resolve(false) // 端口未被占用
        })
        server.close()
      })
      
      server.on('error', () => {
        resolve(true) // 端口被占用
      })
    })
  }

  /**
   * 自动启动代理服务器
   */
  async autoStart() {
    try {
      console.log('🚀 自动启动代理服务器...')

      // 检查端口是否已被占用
      const portInUse = await this.checkPortInUse(this.PORT)
      if (portInUse) {
        console.log(`✅ 端口 ${this.PORT} 已被占用，可能服务已在运行`)
        this.isRunning = true
        return {
          success: true,
          message: '代理服务器已在运行',
          port: this.PORT
        }
      }

      // 启动服务器
      await this.startServer()
      
      return {
        success: true,
        message: '代理服务器启动成功',
        port: this.PORT
      }

    } catch (error) {
      console.error('❌ 自动启动失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 启动服务器
   */
  async startServer() {
    return new Promise((resolve, reject) => {
      try {
        const server = this.app.listen(this.PORT, () => {
          console.log(`✅ 自动代理服务器启动成功，端口: ${this.PORT}`)
          console.log(`🌐 健康检查: http://localhost:${this.PORT}/health`)
          this.isRunning = true
          resolve(server)
        })

        server.on('error', (error) => {
          console.error('❌ 服务器启动失败:', error)
          this.isRunning = false
          reject(error)
        })

        // 优雅关闭处理
        process.on('SIGINT', () => {
          console.log('\n📝 收到退出信号，正在关闭服务器...')
          server.close(() => {
            console.log('✅ 服务器已关闭')
            process.exit(0)
          })
        })

        process.on('SIGTERM', () => {
          console.log('\n📝 收到终止信号，正在关闭服务器...')
          server.close(() => {
            console.log('✅ 服务器已关闭')
            process.exit(0)
          })
        })

      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * 静态启动方法
   */
  static async start() {
    const service = new AutoProxyService()
    return await service.autoStart()
  }
}

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason)
})

// 如果直接运行此文件
if (require.main === module) {
  console.log('🚀 启动自动代理服务...')
  
  AutoProxyService.start().then((result) => {
    if (result.success) {
      console.log('🎉 自动代理服务启动完成')
    } else {
      console.error('❌ 自动代理服务启动失败:', result.error)
      process.exit(1)
    }
  }).catch((error) => {
    console.error('❌ 启动过程中发生错误:', error)
    process.exit(1)
  })
}

module.exports = AutoProxyService
