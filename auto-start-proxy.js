/**
 * 自动启动代理服务器脚本
 * 这个脚本会在后台启动代理服务器，避免用户手动操作
 */

const { spawn } = require('child_process')
const path = require('path')
const fs = require('fs')

class AutoProxyStarter {
  constructor() {
    this.proxyProcess = null
    this.isRunning = false
    this.port = 3001
  }

  /**
   * 检查端口是否被占用
   */
  async checkPortInUse(port) {
    return new Promise((resolve) => {
      const net = require('net')
      const server = net.createServer()
      
      server.listen(port, () => {
        server.once('close', () => {
          resolve(false) // 端口未被占用
        })
        server.close()
      })
      
      server.on('error', () => {
        resolve(true) // 端口被占用
      })
    })
  }

  /**
   * 启动代理服务器
   */
  async startProxy() {
    try {
      // 检查端口是否已被占用
      const portInUse = await this.checkPortInUse(this.port)
      if (portInUse) {
        console.log(`✅ 代理服务器已在端口 ${this.port} 运行`)
        this.isRunning = true
        return true
      }

      console.log('🚀 正在启动代理服务器...')

      // 确保proxy-server.js文件存在
      const proxyServerPath = path.join(__dirname, 'proxy-server.js')
      if (!fs.existsSync(proxyServerPath)) {
        throw new Error('proxy-server.js 文件不存在')
      }

      // 启动代理服务器进程
      this.proxyProcess = spawn('node', [proxyServerPath], {
        detached: true,
        stdio: ['ignore', 'pipe', 'pipe']
      })

      // 监听进程输出
      this.proxyProcess.stdout.on('data', (data) => {
        console.log(`代理服务器: ${data.toString().trim()}`)
      })

      this.proxyProcess.stderr.on('data', (data) => {
        console.error(`代理服务器错误: ${data.toString().trim()}`)
      })

      // 监听进程退出
      this.proxyProcess.on('exit', (code) => {
        console.log(`代理服务器进程退出，退出码: ${code}`)
        this.isRunning = false
        this.proxyProcess = null
      })

      // 监听进程错误
      this.proxyProcess.on('error', (error) => {
        console.error('启动代理服务器失败:', error)
        this.isRunning = false
        this.proxyProcess = null
      })

      // 等待一段时间确保服务器启动
      await new Promise(resolve => setTimeout(resolve, 2000))

      // 验证服务器是否成功启动
      const isNowRunning = await this.checkPortInUse(this.port)
      if (isNowRunning) {
        console.log('✅ 代理服务器启动成功')
        this.isRunning = true
        
        // 让进程在后台运行
        this.proxyProcess.unref()
        
        return true
      } else {
        throw new Error('代理服务器启动失败')
      }

    } catch (error) {
      console.error('❌ 启动代理服务器时出错:', error)
      this.isRunning = false
      return false
    }
  }

  /**
   * 停止代理服务器
   */
  stopProxy() {
    if (this.proxyProcess) {
      console.log('🛑 正在停止代理服务器...')
      this.proxyProcess.kill('SIGTERM')
      this.proxyProcess = null
      this.isRunning = false
    }
  }

  /**
   * 获取状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      port: this.port,
      processId: this.proxyProcess ? this.proxyProcess.pid : null
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const starter = new AutoProxyStarter()
  
  starter.startProxy().then((success) => {
    if (success) {
      console.log('🎉 代理服务器自动启动完成')
      
      // 监听进程退出信号
      process.on('SIGINT', () => {
        console.log('\n📝 收到退出信号，正在关闭代理服务器...')
        starter.stopProxy()
        process.exit(0)
      })
      
      process.on('SIGTERM', () => {
        console.log('\n📝 收到终止信号，正在关闭代理服务器...')
        starter.stopProxy()
        process.exit(0)
      })
      
    } else {
      console.error('❌ 代理服务器自动启动失败')
      process.exit(1)
    }
  }).catch((error) => {
    console.error('❌ 启动过程中发生错误:', error)
    process.exit(1)
  })
}

module.exports = AutoProxyStarter
