/**
 * 浏览器扩展后台脚本
 * 处理跨域请求和消息传递
 */

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener(() => {
  console.log('小说解析助手已安装')
})

// 监听来自网页的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('收到消息:', request)

  switch (request.action) {
    case 'ping':
      // 健康检查
      sendResponse({ success: true, message: '扩展正常运行' })
      break

    case 'fetchPage':
      // 获取页面内容
      handleFetchPage(request, sendResponse)
      return true // 保持消息通道开放

    case 'parseBookInfo':
      // 解析书籍信息
      handleParseBookInfo(request, sendResponse)
      return true

    case 'getStatus':
      // 获取扩展状态
      sendResponse({
        success: true,
        status: 'active',
        version: chrome.runtime.getManifest().version
      })
      break

    default:
      sendResponse({ success: false, error: '未知的操作' })
  }
})

/**
 * 处理页面获取请求
 */
async function handleFetchPage(request, sendResponse) {
  try {
    const { url, options = {} } = request

    if (!url) {
      sendResponse({ success: false, error: '缺少URL参数' })
      return
    }

    console.log(`获取页面: ${url}`)

    // 发送HTTP请求
    const response = await fetch(url, {
      method: options.method || 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        ...options.headers
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const html = await response.text()

    sendResponse({
      success: true,
      html: html,
      status: response.status,
      headers: Object.fromEntries(response.headers.entries()),
      url: response.url
    })

  } catch (error) {
    console.error('获取页面失败:', error)
    sendResponse({
      success: false,
      error: error.message
    })
  }
}

/**
 * 处理书籍信息解析请求
 */
async function handleParseBookInfo(request, sendResponse) {
  try {
    const { bookId, url } = request

    if (!bookId && !url) {
      sendResponse({ success: false, error: '缺少书籍ID或URL' })
      return
    }

    // 构建页面URL
    const pageUrl = url || `https://fanqienovel.com/page/${bookId}`

    // 获取页面内容
    const pageResponse = await fetch(pageUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })

    if (!pageResponse.ok) {
      throw new Error(`获取页面失败: ${pageResponse.status}`)
    }

    const html = await pageResponse.text()

    // 解析书籍信息
    const bookInfo = parseBookInfoFromHtml(html, bookId)

    sendResponse({
      success: true,
      data: bookInfo,
      method: 'extension'
    })

  } catch (error) {
    console.error('解析书籍信息失败:', error)
    sendResponse({
      success: false,
      error: error.message
    })
  }
}

/**
 * 从HTML中解析书籍信息
 */
function parseBookInfoFromHtml(html, bookId) {
  try {
    // 创建DOM解析器
    const parser = new DOMParser()
    const doc = parser.parseFromString(html, 'text/html')

    // 提取书名
    let novelName = '未知书名'
    const titleElement = doc.querySelector('h1')
    if (titleElement && titleElement.textContent.trim()) {
      novelName = titleElement.textContent.trim()
    }

    // 提取作者
    let author = '未知作者'
    const authorSelectors = [
      '.author-name-text',
      '.author-name',
      '[class*="author"]',
      '.info-author'
    ]
    
    for (const selector of authorSelectors) {
      const authorElement = doc.querySelector(selector)
      if (authorElement && authorElement.textContent.trim()) {
        author = authorElement.textContent.trim()
        break
      }
    }

    // 提取简介
    let description = '暂无简介'
    const descSelectors = [
      '.page-abstract-content',
      '.book-intro',
      '.description',
      '[class*="abstract"]',
      '[class*="intro"]'
    ]
    
    for (const selector of descSelectors) {
      const descElement = doc.querySelector(selector)
      if (descElement && descElement.textContent.trim()) {
        description = descElement.textContent.trim()
        break
      }
    }

    // 提取封面
    let coverUrl = ''
    const coverElement = doc.querySelector('img[class*="cover"], .book-cover img, .cover-img')
    if (coverElement) {
      coverUrl = coverElement.src || coverElement.getAttribute('data-src') || ''
    }

    return {
      novelId: bookId,
      novelName: novelName,
      author: author,
      description: description,
      coverUrl: coverUrl,
      totalChapters: 0,
      parseTime: new Date().toISOString(),
      source: 'extension'
    }

  } catch (error) {
    console.error('HTML解析失败:', error)
    return {
      novelId: bookId,
      novelName: `小说_${bookId}`,
      author: '未知作者',
      description: '解析失败',
      coverUrl: '',
      totalChapters: 0,
      parseTime: new Date().toISOString(),
      source: 'extension_fallback'
    }
  }
}

// 监听标签页更新
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    // 检查是否是支持的小说网站
    const supportedSites = [
      'fanqienovel.com',
      'changdunovel.com'
    ]
    
    const isSupported = supportedSites.some(site => tab.url.includes(site))
    
    if (isSupported) {
      console.log('检测到支持的小说网站:', tab.url)
      // 可以在这里注入内容脚本或执行其他操作
    }
  }
})

// 处理扩展图标点击
chrome.action.onClicked.addListener((tab) => {
  // 打开选项页面或执行其他操作
  chrome.tabs.create({
    url: chrome.runtime.getURL('popup.html')
  })
})
