/**
 * Vercel云端代理服务器
 * 用于绕过CORS限制，支持小说网站内容获取
 */

export default async function handler(req, res) {
  // 设置CORS头
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
    'Access-Control-Max-Age': '86400'
  }

  // 设置响应头
  Object.entries(corsHeaders).forEach(([key, value]) => {
    res.setHeader(key, value)
  })

  // 处理预检请求
  if (req.method === 'OPTIONS') {
    res.status(200).end()
    return
  }

  // 健康检查
  if (req.url === '/api/proxy/health') {
    res.status(200).json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'vercel-proxy'
    })
    return
  }

  try {
    let targetUrl
    let headers = {}

    // 支持GET和POST请求
    if (req.method === 'GET') {
      targetUrl = req.query.url
      if (req.query.headers) {
        try {
          headers = JSON.parse(req.query.headers)
        } catch (e) {
          // 忽略解析错误
        }
      }
    } else if (req.method === 'POST') {
      const body = req.body
      targetUrl = body.url
      headers = body.headers || {}
    }

    if (!targetUrl) {
      res.status(400).json({
        success: false,
        error: '缺少URL参数'
      })
      return
    }

    // 验证URL格式
    try {
      new URL(targetUrl)
    } catch (e) {
      res.status(400).json({
        success: false,
        error: '无效的URL格式'
      })
      return
    }

    // 安全检查 - 只允许特定域名
    const allowedDomains = [
      'fanqienovel.com',
      'changdunovel.com',
      'qidian.com',
      'zongheng.com',
      'jjwxc.net'
    ]

    const urlObj = new URL(targetUrl)
    const isAllowed = allowedDomains.some(domain => 
      urlObj.hostname === domain || urlObj.hostname.endsWith('.' + domain)
    )

    if (!isAllowed) {
      res.status(403).json({
        success: false,
        error: '不允许访问该域名'
      })
      return
    }

    console.log(`代理请求: ${targetUrl}`)

    // 设置默认请求头
    const requestHeaders = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'Accept-Encoding': 'gzip, deflate, br',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'none',
      'Cache-Control': 'max-age=0',
      ...headers
    }

    // 发送请求
    const response = await fetch(targetUrl, {
      method: 'GET',
      headers: requestHeaders,
      redirect: 'follow'
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    // 获取响应内容
    const html = await response.text()

    // 返回成功响应
    res.status(200).json({
      success: true,
      html: html,
      status: response.status,
      headers: Object.fromEntries(response.headers.entries()),
      url: targetUrl,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('代理请求失败:', error.message)
    
    // 返回错误响应
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString(),
      details: {
        name: error.name,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      }
    })
  }
}

// 导出配置
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
    responseLimit: '8mb',
  },
}
