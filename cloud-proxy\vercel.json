{"version": 2, "functions": {"api/proxy.js": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Requested-With"}, {"key": "Access-Control-Max-Age", "value": "86400"}]}], "rewrites": [{"source": "/proxy/(.*)", "destination": "/api/proxy"}]}