# 增强的跨域解决方案

## 1. 云端代理服务器（推荐）

### 优势
- 无需用户手动启动本地服务器
- 支持所有设备（包括移动设备）
- 更好的稳定性和可用性

### 实现方案

#### A. 使用免费的CORS代理服务
```javascript
// 在 proxy-manager.js 中添加云端代理
constructor() {
  this.proxyUrls = [
    'http://localhost:3001',                    // 本地代理服务器
    'http://127.0.0.1:3001',                   // 备用本地地址
    'https://cors-anywhere.herokuapp.com',      // 免费CORS代理
    'https://api.allorigins.win',              // AllOrigins代理
    'https://thingproxy.freeboard.io',         // ThingProxy代理
    'https://cors-proxy.htmldriven.com'        // HTMLDriven代理
  ]
}

// 云端代理请求方法
async fetchViaCloudProxy(url, proxyUrl) {
  const methods = {
    'cors-anywhere': async (targetUrl, proxy) => {
      const response = await fetch(`${proxy}/${targetUrl}`, {
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        }
      })
      return response.text()
    },
    
    'allorigins': async (targetUrl, proxy) => {
      const response = await fetch(`${proxy}/get?url=${encodeURIComponent(targetUrl)}`)
      const data = await response.json()
      return data.contents
    },
    
    'thingproxy': async (targetUrl, proxy) => {
      const response = await fetch(`${proxy}/fetch/${targetUrl}`)
      return response.text()
    }
  }
  
  // 根据代理类型选择方法
  const method = this.detectProxyType(proxyUrl)
  return methods[method](url, proxyUrl)
}
```

#### B. 部署自己的云端代理服务器

**使用Vercel部署（免费）：**

```javascript
// api/proxy.js (Vercel函数)
export default async function handler(req, res) {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type')
  
  if (req.method === 'OPTIONS') {
    res.status(200).end()
    return
  }
  
  try {
    const { url } = req.body
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })
    
    const html = await response.text()
    res.json({ success: true, html })
  } catch (error) {
    res.status(500).json({ success: false, error: error.message })
  }
}
```

**使用Netlify Functions：**

```javascript
// netlify/functions/proxy.js
exports.handler = async (event, context) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
  }
  
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers }
  }
  
  try {
    const { url } = JSON.parse(event.body)
    const response = await fetch(url)
    const html = await response.text()
    
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ success: true, html })
    }
  } catch (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ success: false, error: error.message })
    }
  }
}
```

## 2. 浏览器扩展方案

### Chrome扩展实现
```javascript
// manifest.json
{
  "manifest_version": 3,
  "name": "小说下载器助手",
  "version": "1.0",
  "permissions": ["activeTab", "storage"],
  "host_permissions": ["https://fanqienovel.com/*"],
  "background": {
    "service_worker": "background.js"
  },
  "content_scripts": [{
    "matches": ["https://fanqienovel.com/*"],
    "js": ["content.js"]
  }]
}

// background.js
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'fetchPage') {
    fetch(request.url)
      .then(response => response.text())
      .then(html => sendResponse({ success: true, html }))
      .catch(error => sendResponse({ success: false, error: error.message }))
    return true // 保持消息通道开放
  }
})
```

## 3. Service Worker代理

```javascript
// sw-proxy.js
self.addEventListener('fetch', event => {
  if (event.request.url.includes('/api/proxy/')) {
    event.respondWith(handleProxyRequest(event.request))
  }
})

async function handleProxyRequest(request) {
  try {
    const url = new URL(request.url)
    const targetUrl = url.searchParams.get('url')
    
    const response = await fetch(targetUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })
    
    const html = await response.text()
    
    return new Response(JSON.stringify({ success: true, html }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  } catch (error) {
    return new Response(JSON.stringify({ success: false, error: error.message }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  }
}
```

## 4. WebRTC数据通道方案

```javascript
// 使用WebRTC绕过CORS（高级方案）
class WebRTCProxy {
  constructor() {
    this.connections = new Map()
  }
  
  async createProxyConnection() {
    const pc = new RTCPeerConnection({
      iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
    })
    
    const dataChannel = pc.createDataChannel('proxy', {
      ordered: true
    })
    
    dataChannel.onopen = () => {
      console.log('WebRTC代理通道已建立')
    }
    
    dataChannel.onmessage = (event) => {
      const data = JSON.parse(event.data)
      this.handleProxyRequest(data)
    }
    
    return { pc, dataChannel }
  }
  
  async fetchViaWebRTC(url) {
    // 通过WebRTC数据通道发送请求
    // 这需要一个对等端来处理实际的HTTP请求
  }
}
```

## 5. 推荐的综合解决方案

基于你的现有代码，我建议以下增强方案：

### 方案一：增强现有代理管理器

```javascript
// 在 proxy-manager.js 中添加云端代理支持
class EnhancedProxyManager extends ProxyManager {
  constructor() {
    super()
    this.cloudProxies = [
      {
        name: 'AllOrigins',
        url: 'https://api.allorigins.win',
        type: 'allorigins',
        available: true
      },
      {
        name: 'CORS Anywhere',
        url: 'https://cors-anywhere.herokuapp.com',
        type: 'cors-anywhere',
        available: true
      },
      {
        name: 'ThingProxy',
        url: 'https://thingproxy.freeboard.io',
        type: 'thingproxy',
        available: true
      }
    ]
  }

  async fetchViaCloudProxy(targetUrl) {
    for (const proxy of this.cloudProxies) {
      if (!proxy.available) continue

      try {
        const result = await this.tryCloudProxy(targetUrl, proxy)
        if (result.success) {
          console.log(`✅ 云端代理 ${proxy.name} 请求成功`)
          return result
        }
      } catch (error) {
        console.warn(`⚠️ 云端代理 ${proxy.name} 失败:`, error.message)
        proxy.available = false
        continue
      }
    }

    throw new Error('所有云端代理都不可用')
  }

  async tryCloudProxy(targetUrl, proxy) {
    switch (proxy.type) {
      case 'allorigins':
        return await this.fetchViaAllOrigins(targetUrl, proxy.url)
      case 'cors-anywhere':
        return await this.fetchViaCorsAnywhere(targetUrl, proxy.url)
      case 'thingproxy':
        return await this.fetchViaThingProxy(targetUrl, proxy.url)
      default:
        throw new Error(`未知的代理类型: ${proxy.type}`)
    }
  }

  async fetchViaAllOrigins(targetUrl, proxyUrl) {
    const response = await fetch(`${proxyUrl}/get?url=${encodeURIComponent(targetUrl)}`)
    if (!response.ok) throw new Error(`请求失败: ${response.status}`)

    const data = await response.json()
    return {
      success: true,
      html: data.contents,
      status: data.status.http_code
    }
  }

  async fetchViaCorsAnywhere(targetUrl, proxyUrl) {
    const response = await fetch(`${proxyUrl}/${targetUrl}`, {
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      }
    })

    if (!response.ok) throw new Error(`请求失败: ${response.status}`)

    return {
      success: true,
      html: await response.text(),
      status: response.status
    }
  }

  async fetchViaThingProxy(targetUrl, proxyUrl) {
    const response = await fetch(`${proxyUrl}/fetch/${targetUrl}`)
    if (!response.ok) throw new Error(`请求失败: ${response.status}`)

    return {
      success: true,
      html: await response.text(),
      status: response.status
    }
  }
}
```

### 方案二：部署云端代理服务

**1. 使用Vercel部署（推荐）**

创建 `vercel.json`:
```json
{
  "functions": {
    "api/proxy.js": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Access-Control-Allow-Origin",
          "value": "*"
        },
        {
          "key": "Access-Control-Allow-Methods",
          "value": "GET, POST, OPTIONS"
        },
        {
          "key": "Access-Control-Allow-Headers",
          "value": "Content-Type"
        }
      ]
    }
  ]
}
```

**2. 使用Railway部署**

创建 `railway.toml`:
```toml
[build]
builder = "nixpacks"

[deploy]
startCommand = "node proxy-server.js"

[[services]]
name = "proxy-server"
```

### 方案三：浏览器扩展辅助

创建简单的Chrome扩展来辅助跨域请求：

**manifest.json:**
```json
{
  "manifest_version": 3,
  "name": "小说解析助手",
  "version": "1.0",
  "permissions": ["activeTab"],
  "host_permissions": ["https://fanqienovel.com/*"],
  "background": {
    "service_worker": "background.js"
  }
}
```

**background.js:**
```javascript
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'fetchNovelPage') {
    fetch(request.url)
      .then(response => response.text())
      .then(html => sendResponse({ success: true, html }))
      .catch(error => sendResponse({ success: false, error: error.message }))
    return true
  }
})
```

## 6. 实施建议

### 立即可实施的改进：

1. **添加更多云端代理**
2. **改进错误处理和重试机制**
3. **添加代理性能监控**
4. **实现智能代理选择**

### 中期改进：

1. **部署专用云端代理服务**
2. **开发浏览器扩展**
3. **添加缓存机制**

### 长期规划：

1. **考虑使用官方API**
2. **开发桌面应用版本**
3. **实现分布式代理网络**
