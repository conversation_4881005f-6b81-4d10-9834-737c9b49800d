@echo off
chcp 65001 >nul
title 番茄小说自动代理服务

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    番茄小说下载器                            ║
echo ║                  自动代理服务启动器                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 设置工作目录
cd /d "%~dp0"

REM 检查Node.js环境
echo [1/4] 🔍 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Node.js，请先安装Node.js
    echo 📥 下载地址：https://nodejs.org/
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Node.js 环境正常
)

REM 检查自动代理服务文件
echo [2/4] 📁 检查服务文件...
if not exist "auto-proxy-service.js" (
    echo ❌ 未找到auto-proxy-service.js文件
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
) else (
    echo ✅ 自动代理服务文件存在
)

REM 检查并安装依赖
echo [3/4] 📦 检查项目依赖...
if not exist "node_modules" (
    echo 📦 正在安装依赖...
    npm install --silent
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
) else (
    echo ✅ 项目依赖已存在
)

REM 检查端口占用
echo [4/4] 🔍 检查端口3001...
netstat -an | find "3001" | find "LISTENING" >nul
if %errorlevel% equ 0 (
    echo ⚠️  端口3001已被占用
    echo 可能代理服务器已在运行
    echo.
    echo 选择操作：
    echo [1] 继续启动（可能失败）
    echo [2] 强制重启
    echo [3] 退出
    choice /c 123 /n /m "请选择 (1/2/3): "
    
    if errorlevel 3 exit /b 0
    if errorlevel 2 (
        echo 🔄 正在强制重启...
        taskkill /f /im node.exe >nul 2>&1
        timeout /t 2 >nul
    )
) else (
    echo ✅ 端口3001可用
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║  🚀 启动自动代理服务                                        ║
echo ║  📡 端口: 3001                                              ║
echo ║  🌐 健康检查: http://localhost:3001/health                   ║
echo ║  🔄 自动启动: http://localhost:3001/auto-start               ║
echo ║  🛑 按 Ctrl+C 停止服务                                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 启动自动代理服务
echo 🚀 正在启动自动代理服务...
node auto-proxy-service.js

echo.
echo 📝 自动代理服务已停止
echo 按任意键退出...
pause >nul
