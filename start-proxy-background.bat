@echo off
chcp 65001 >nul

REM 设置工作目录
cd /d "%~dp0"

REM 检查是否已经在运行
netstat -an | find "3001" | find "LISTENING" >nul
if %errorlevel% equ 0 (
    echo 代理服务器已在运行
    exit /b 0
)

REM 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到Node.js
    exit /b 1
)

REM 检查服务文件
if not exist "auto-proxy-service.js" (
    echo 错误：未找到auto-proxy-service.js文件
    exit /b 1
)

REM 在后台启动代理服务器（无窗口）
start /b /min "" node auto-proxy-service.js >nul 2>&1

REM 等待服务启动
timeout /t 3 >nul

REM 验证启动结果
netstat -an | find "3001" | find "LISTENING" >nul
if %errorlevel% equ 0 (
    echo 代理服务器后台启动成功
    exit /b 0
) else (
    echo 代理服务器启动失败
    exit /b 1
)
