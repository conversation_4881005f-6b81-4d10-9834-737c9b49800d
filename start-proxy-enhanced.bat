@echo off
chcp 65001 >nul
title 小说下载器 - 代理服务器

echo.
echo ========================================
echo    小说下载器 - 代理服务器启动器
echo ========================================
echo.

:: 检查Node.js是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未检测到Node.js
    echo.
    echo 请先安装Node.js：
    echo 1. 访问 https://nodejs.org/
    echo 2. 下载并安装LTS版本
    echo 3. 重新运行此脚本
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js 已安装
node --version

:: 检查代理服务器文件是否存在
if not exist "proxy-server.js" (
    echo ❌ 错误：未找到 proxy-server.js 文件
    echo.
    echo 请确保在正确的目录下运行此脚本
    echo 当前目录：%CD%
    echo.
    pause
    exit /b 1
)

echo ✅ 代理服务器文件存在

:: 检查端口3001是否被占用
netstat -an | find "3001" | find "LISTENING" >nul
if not errorlevel 1 (
    echo ⚠️  警告：端口3001已被占用
    echo.
    echo 正在尝试终止占用进程...
    for /f "tokens=5" %%a in ('netstat -ano ^| find "3001" ^| find "LISTENING"') do (
        echo 终止进程 PID: %%a
        taskkill /PID %%a /F >nul 2>&1
    )
    timeout /t 2 >nul
)

echo.
echo 🚀 启动代理服务器...
echo.
echo 代理服务器信息：
echo - 端口：3001
echo - 健康检查：http://localhost:3001/health
echo - 代理接口：http://localhost:3001/proxy/fetch-page
echo.
echo 💡 提示：
echo - 保持此窗口打开以维持代理服务器运行
echo - 在浏览器中刷新页面以重新检测代理状态
echo - 按 Ctrl+C 可停止代理服务器
echo.
echo ========================================
echo.

:: 启动代理服务器
node proxy-server.js

echo.
echo 代理服务器已停止
pause
