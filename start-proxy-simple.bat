@echo off
chcp 65001 >nul
title 番茄小说代理服务器
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    番茄小说下载器                            ║
echo ║                   代理服务器启动器                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 快速检查并启动
echo 🔍 检查环境...

REM 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Node.js，请先安装Node.js
    echo 📥 下载地址：https://nodejs.org/
    echo.
    pause
    exit /b 1
)

REM 检查代理服务器文件
if not exist "proxy-server.js" (
    echo ❌ 未找到proxy-server.js文件
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

REM 快速安装依赖（如果需要）
if not exist "node_modules" (
    echo 📦 安装依赖中...
    npm install --silent
)

echo ✅ 环境检查完成
echo.

REM 检查端口占用
echo 🔍 检查端口3001...
netstat -an | find "3001" | find "LISTENING" >nul
if %errorlevel% equ 0 (
    echo ⚠️  端口3001已被占用
    echo 可能代理服务器已在运行，或被其他程序占用
    echo.
    echo 选择操作：
    echo [1] 继续启动（可能失败）
    echo [2] 退出
    choice /c 12 /n /m "请选择 (1/2): "
    if errorlevel 2 exit /b 0
    echo.
)

echo ╔══════════════════════════════════════════════════════════════╗
echo ║  🚀 启动代理服务器                                          ║
echo ║  📡 端口: 3001                                              ║
echo ║  🌐 健康检查: http://localhost:3001/health                   ║
echo ║  🛑 按 Ctrl+C 停止服务器                                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 启动代理服务器
node proxy-server.js

echo.
echo 📝 代理服务器已停止
echo 按任意键退出...
pause >nul
