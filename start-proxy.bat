@echo off
chcp 65001 >nul
title 番茄小说下载器 - 代理服务器
echo ========================================
echo    番茄小说下载器 - 代理服务器启动器
echo ========================================
echo.

REM 检查Node.js是否安装
echo [1/4] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到Node.js，请先安装Node.js
    echo 📥 下载地址：https://nodejs.org/
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Node.js 环境正常
)

REM 检查代理服务器文件
echo [2/4] 检查代理服务器文件...
if not exist "proxy-server.js" (
    echo ❌ 错误：未找到 proxy-server.js 文件
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
) else (
    echo ✅ 代理服务器文件存在
)

REM 检查端口是否被占用
echo [3/4] 检查端口 3001...
netstat -an | find "3001" | find "LISTENING" >nul
if %errorlevel% equ 0 (
    echo ⚠️  端口 3001 已被占用，可能代理服务器已在运行
    echo 如果需要重启，请先关闭现有进程
    echo.
    choice /c YN /m "是否继续启动 (Y/N)"
    if errorlevel 2 exit /b 0
) else (
    echo ✅ 端口 3001 可用
)

REM 检查并安装依赖
echo [4/4] 检查项目依赖...
if not exist "node_modules" (
    echo 📦 正在安装依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 错误：依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
) else (
    echo ✅ 项目依赖已存在
)

echo.
echo ========================================
echo 🚀 正在启动代理服务器...
echo 📡 端口：3001
echo 🌐 健康检查：http://localhost:3001/health
echo 🛑 按 Ctrl+C 停止服务器
echo ========================================
echo.

REM 启动代理服务器
node proxy-server.js

echo.
echo 代理服务器已停止
pause
