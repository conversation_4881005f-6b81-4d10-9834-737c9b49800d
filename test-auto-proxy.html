<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动代理服务器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-running {
            background-color: #67c23a;
        }
        .status-stopped {
            background-color: #f56c6c;
        }
        .status-checking {
            background-color: #e6a23c;
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .test-button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .test-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .log-section {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .test-input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 自动代理服务器功能测试</h1>
        
        <div class="status-section">
            <h3>📊 代理服务器状态</h3>
            <div id="proxyStatus">
                <span class="status-indicator status-checking"></span>
                <span>检查中...</span>
            </div>
            <div style="margin-top: 15px;">
                <button class="test-button" onclick="checkProxyStatus()">🔍 检查状态</button>
                <button class="test-button" onclick="showInstructions()">📖 启动说明</button>
                <button class="test-button" onclick="testExtraction()">🧪 测试提取</button>
            </div>
        </div>

        <div class="status-section">
            <h3>🔗 书籍ID提取测试</h3>
            <input type="text" id="testUrl" class="test-input" 
                   placeholder="输入番茄小说链接进行测试"
                   value="https://changdunovel.com/wap/share-v2.html?book_id=7351352518972034110&share_type=0">
            <button class="test-button" onclick="testBookIdExtraction()">提取书籍ID</button>
            <div id="extractionResult"></div>
        </div>

        <div class="status-section">
            <h3>📝 操作日志</h3>
            <div id="logContainer" class="log-section"></div>
            <button class="test-button" onclick="clearLogs()">清空日志</button>
        </div>
    </div>

    <script>
        let logs = [];

        // 添加日志
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logs.push(logEntry);
            
            const logContainer = document.getElementById('logContainer');
            const logElement = document.createElement('div');
            logElement.textContent = logEntry;
            logElement.style.color = type === 'error' ? '#f56c6c' : 
                                   type === 'success' ? '#67c23a' : 
                                   type === 'warning' ? '#e6a23c' : '#333';
            logContainer.appendChild(logElement);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 清空日志
        function clearLogs() {
            logs = [];
            document.getElementById('logContainer').innerHTML = '';
        }

        // 检查代理服务器状态
        async function checkProxyStatus() {
            const statusElement = document.getElementById('proxyStatus');
            statusElement.innerHTML = '<span class="status-indicator status-checking"></span><span>检查中...</span>';
            
            addLog('开始检查代理服务器状态...');
            
            try {
                const response = await fetch('http://localhost:3001/health', {
                    method: 'GET',
                    timeout: 5000
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.status === 'ok') {
                        statusElement.innerHTML = '<span class="status-indicator status-running"></span><span>运行中 ✅</span>';
                        addLog('代理服务器运行正常', 'success');
                        return true;
                    }
                }
                
                throw new Error('代理服务器响应异常');
                
            } catch (error) {
                statusElement.innerHTML = '<span class="status-indicator status-stopped"></span><span>未运行 ❌</span>';
                addLog(`代理服务器检查失败: ${error.message}`, 'error');
                return false;
            }
        }

        // 显示启动说明
        function showInstructions() {
            alert(`代理服务器启动方法：

方法1（推荐）：
双击项目根目录下的 start-proxy.bat 文件

方法2：
在项目根目录打开命令行，运行：
node proxy-server.js

注意：
• 代理服务器运行在端口 3001
• 启动后点击"检查状态"按钮验证
• 关闭浏览器不会影响代理服务器运行
• 代理服务器用于解析书籍信息`);
        }

        // 提取书籍ID
        function extractBookId(url) {
            if (!url) return null;

            // 检查是否为纯数字ID
            if (/^\d+$/.test(url.trim())) {
                return url.trim();
            }

            // 支持多种URL格式，包括番茄小说分享链接
            const patterns = [
                /\/page\/(\d+)/,           // /page/123456
                /\/reader\/(\d+)/,         // /reader/123456
                /[?&]book_id=(\d+)/,       // ?book_id=123456 (支持分享链接中的book_id参数)
                /[?&]bookId=(\d+)/,        // ?bookId=123456
                /(\d{19})/                 // 直接的19位数字
            ];

            for (const pattern of patterns) {
                const match = url.match(pattern);
                if (match && match[1]) {
                    return match[1];
                }
            }

            return null;
        }

        // 测试书籍ID提取
        function testBookIdExtraction() {
            const url = document.getElementById('testUrl').value;
            const resultElement = document.getElementById('extractionResult');
            
            addLog(`测试提取书籍ID: ${url}`);
            
            if (!url.trim()) {
                resultElement.innerHTML = '<div class="error">请输入要测试的链接</div>';
                addLog('提取失败: 输入为空', 'error');
                return;
            }

            const bookId = extractBookId(url);
            
            if (bookId) {
                resultElement.innerHTML = `
                    <div class="success">
                        <strong>✅ 提取成功！</strong><br>
                        书籍ID: <strong>${bookId}</strong>
                    </div>
                `;
                addLog(`提取成功: ${bookId}`, 'success');
            } else {
                resultElement.innerHTML = `
                    <div class="error">
                        <strong>❌ 提取失败</strong><br>
                        无法从输入中提取有效的书籍ID
                    </div>
                `;
                addLog('提取失败: 无法识别的URL格式', 'error');
            }
        }

        // 综合测试
        async function testExtraction() {
            addLog('开始综合测试...');
            
            // 1. 检查代理服务器
            const proxyRunning = await checkProxyStatus();
            
            if (!proxyRunning) {
                addLog('综合测试终止: 代理服务器未运行', 'warning');
                alert('请先启动代理服务器再进行测试');
                return;
            }
            
            // 2. 测试书籍ID提取
            const testUrl = document.getElementById('testUrl').value || 
                           'https://changdunovel.com/wap/share-v2.html?book_id=7351352518972034110';
            document.getElementById('testUrl').value = testUrl;
            testBookIdExtraction();
            
            addLog('综合测试完成', 'success');
        }

        // 页面加载时自动检查状态
        window.onload = function() {
            addLog('页面加载完成，开始初始化...');
            checkProxyStatus();
        };
    </script>
</body>
</html>
