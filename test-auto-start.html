<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动启动代理服务器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .status-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-running { background-color: #67c23a; }
        .status-stopped { background-color: #f56c6c; }
        .status-starting { 
            background-color: #409eff; 
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
            margin-top: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background-color: #fafafa;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 自动启动代理服务器测试</h1>
        
        <!-- 状态面板 -->
        <div class="status-panel">
            <div class="status-card">
                <h3>📡 代理服务器状态</h3>
                <div id="proxyStatus">
                    <span class="status-indicator status-starting"></span>
                    <span>检查中...</span>
                </div>
                <div style="margin-top: 10px; font-size: 12px; color: #666;" id="proxyDetails"></div>
            </div>
            
            <div class="status-card">
                <h3>🔄 自动启动状态</h3>
                <div id="autoStartStatus">
                    <span class="status-indicator status-starting"></span>
                    <span>准备中...</span>
                </div>
                <div style="margin-top: 10px; font-size: 12px; color: #666;" id="autoStartDetails"></div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="test-section">
            <h3>🎮 控制面板</h3>
            <div style="margin-bottom: 15px;">
                <button class="button" onclick="checkStatus()" id="checkBtn">🔍 检查状态</button>
                <button class="button" onclick="autoStart()" id="autoStartBtn">🚀 自动启动</button>
                <button class="button" onclick="testConnection()" id="testBtn">🧪 测试连接</button>
                <button class="button" onclick="clearLogs()">🗑️ 清空日志</button>
            </div>
            
            <!-- 进度条 -->
            <div id="progressContainer" style="display: none;">
                <div>启动进度：</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div id="progressText">准备启动...</div>
            </div>
        </div>

        <!-- 启动方法测试 -->
        <div class="test-section">
            <h3>🔧 启动方法测试</h3>
            <div style="margin-bottom: 15px;">
                <button class="button" onclick="testMethod('api')">📡 API启动</button>
                <button class="button" onclick="testMethod('iframe')">🖼️ iframe启动</button>
                <button class="button" onclick="testMethod('worker')">👷 Worker启动</button>
                <button class="button" onclick="testMethod('script')">📜 脚本启动</button>
            </div>
            <div id="methodResults"></div>
        </div>

        <!-- 日志区域 -->
        <div class="test-section">
            <h3>📝 系统日志</h3>
            <div id="logArea" class="log-area"></div>
        </div>
    </div>

    <script>
        let logs = []
        let isStarting = false

        // 添加日志
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString()
            const logEntry = `[${timestamp}] ${message}`
            logs.push(logEntry)
            
            const logArea = document.getElementById('logArea')
            const logElement = document.createElement('div')
            logElement.textContent = logEntry
            logElement.style.color = type === 'error' ? '#f56c6c' : 
                                   type === 'success' ? '#67c23a' : 
                                   type === 'warning' ? '#e6a23c' : '#333'
            logArea.appendChild(logElement)
            logArea.scrollTop = logArea.scrollHeight
        }

        // 清空日志
        function clearLogs() {
            logs = []
            document.getElementById('logArea').innerHTML = ''
        }

        // 更新进度
        function updateProgress(percent, text) {
            const progressContainer = document.getElementById('progressContainer')
            const progressFill = document.getElementById('progressFill')
            const progressText = document.getElementById('progressText')
            
            if (percent > 0) {
                progressContainer.style.display = 'block'
                progressFill.style.width = percent + '%'
                progressText.textContent = text
            } else {
                progressContainer.style.display = 'none'
            }
        }

        // 检查代理服务器状态
        async function checkStatus() {
            try {
                addLog('🔍 检查代理服务器状态...')
                
                const response = await fetch('http://localhost:3001/health', {
                    method: 'GET',
                    timeout: 5000
                })
                
                if (response.ok) {
                    const data = await response.json()
                    updateProxyStatus(true, `运行正常 (${data.service || 'proxy-server'})`)
                    addLog('✅ 代理服务器运行正常', 'success')
                    return true
                } else {
                    throw new Error(`HTTP ${response.status}`)
                }
            } catch (error) {
                updateProxyStatus(false, `连接失败: ${error.message}`)
                addLog(`❌ 代理服务器检查失败: ${error.message}`, 'error')
                return false
            }
        }

        // 更新代理状态显示
        function updateProxyStatus(isRunning, details) {
            const proxyStatus = document.getElementById('proxyStatus')
            const proxyDetails = document.getElementById('proxyDetails')
            
            if (isRunning) {
                proxyStatus.innerHTML = '<span class="status-indicator status-running"></span><span>运行中 ✅</span>'
            } else {
                proxyStatus.innerHTML = '<span class="status-indicator status-stopped"></span><span>未运行 ❌</span>'
            }
            
            proxyDetails.textContent = details
        }

        // 更新自动启动状态
        function updateAutoStartStatus(status, details) {
            const autoStartStatus = document.getElementById('autoStartStatus')
            const autoStartDetails = document.getElementById('autoStartDetails')
            
            const statusMap = {
                'starting': '<span class="status-indicator status-starting"></span><span>启动中 🚀</span>',
                'success': '<span class="status-indicator status-running"></span><span>启动成功 ✅</span>',
                'failed': '<span class="status-indicator status-stopped"></span><span>启动失败 ❌</span>',
                'ready': '<span class="status-indicator status-running"></span><span>就绪 ⚡</span>'
            }
            
            autoStartStatus.innerHTML = statusMap[status] || statusMap['ready']
            autoStartDetails.textContent = details
        }

        // 自动启动代理服务器
        async function autoStart() {
            if (isStarting) {
                addLog('⚠️ 启动进程已在运行中', 'warning')
                return
            }

            try {
                isStarting = true
                document.getElementById('autoStartBtn').disabled = true
                
                addLog('🚀 开始自动启动代理服务器...')
                updateAutoStartStatus('starting', '正在尝试启动...')
                updateProgress(10, '初始化启动进程...')

                // 首先检查是否已经运行
                updateProgress(20, '检查当前状态...')
                const isAlreadyRunning = await checkStatus()
                if (isAlreadyRunning) {
                    addLog('✅ 代理服务器已在运行', 'success')
                    updateAutoStartStatus('success', '服务器已在运行')
                    updateProgress(0, '')
                    return
                }

                // 尝试多种启动方法
                const methods = [
                    { name: 'API启动', func: () => startViaAPI(), progress: 30 },
                    { name: 'iframe启动', func: () => startViaIframe(), progress: 50 },
                    { name: 'Worker启动', func: () => startViaWorker(), progress: 70 },
                    { name: '脚本启动', func: () => startViaScript(), progress: 90 }
                ]

                let success = false
                for (const method of methods) {
                    updateProgress(method.progress, `尝试${method.name}...`)
                    addLog(`🔄 尝试${method.name}...`)
                    
                    try {
                        const result = await method.func()
                        if (result) {
                            addLog(`✅ ${method.name}成功`, 'success')
                            success = true
                            break
                        } else {
                            addLog(`❌ ${method.name}失败`, 'warning')
                        }
                    } catch (error) {
                        addLog(`❌ ${method.name}异常: ${error.message}`, 'error')
                    }
                    
                    // 等待一段时间再尝试下一个方法
                    await new Promise(resolve => setTimeout(resolve, 1000))
                }

                updateProgress(100, '验证启动结果...')
                
                // 等待服务器启动
                await new Promise(resolve => setTimeout(resolve, 3000))
                
                // 验证启动结果
                const finalCheck = await checkStatus()
                if (finalCheck) {
                    updateAutoStartStatus('success', '自动启动成功')
                    addLog('🎉 代理服务器自动启动成功！', 'success')
                } else {
                    updateAutoStartStatus('failed', '所有启动方法都失败')
                    addLog('❌ 所有自动启动方法都失败', 'error')
                }

                updateProgress(0, '')

            } catch (error) {
                addLog(`❌ 自动启动过程异常: ${error.message}`, 'error')
                updateAutoStartStatus('failed', `启动异常: ${error.message}`)
                updateProgress(0, '')
            } finally {
                isStarting = false
                document.getElementById('autoStartBtn').disabled = false
            }
        }

        // 通过API启动
        async function startViaAPI() {
            try {
                const response = await fetch('http://localhost:3001/auto-start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    timeout: 5000
                })
                return response.ok
            } catch (error) {
                return false
            }
        }

        // 通过iframe启动
        async function startViaIframe() {
            return new Promise((resolve) => {
                try {
                    const iframe = document.createElement('iframe')
                    iframe.style.display = 'none'
                    document.body.appendChild(iframe)

                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document
                    iframeDoc.open()
                    iframeDoc.write(`
                        <script>
                        (async function() {
                            try {
                                await fetch('http://localhost:3001/auto-start', {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' }
                                });
                            } catch (e) {}
                        })();
                        </script>
                    `)
                    iframeDoc.close()

                    setTimeout(() => {
                        document.body.removeChild(iframe)
                        resolve(true)
                    }, 2000)
                } catch (error) {
                    resolve(false)
                }
            })
        }

        // 通过Worker启动
        async function startViaWorker() {
            if (!window.Worker) return false

            return new Promise((resolve) => {
                try {
                    const workerScript = `
                        self.onmessage = async function(e) {
                            try {
                                await fetch('http://localhost:3001/auto-start', {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' }
                                });
                                self.postMessage({ success: true });
                            } catch (error) {
                                self.postMessage({ success: false });
                            }
                        };
                    `

                    const blob = new Blob([workerScript], { type: 'application/javascript' })
                    const worker = new Worker(URL.createObjectURL(blob))

                    worker.onmessage = (e) => {
                        worker.terminate()
                        URL.revokeObjectURL(blob)
                        resolve(e.data.success)
                    }

                    worker.onerror = () => {
                        worker.terminate()
                        URL.revokeObjectURL(blob)
                        resolve(false)
                    }

                    worker.postMessage({ action: 'start' })

                    setTimeout(() => {
                        worker.terminate()
                        URL.revokeObjectURL(blob)
                        resolve(false)
                    }, 3000)
                } catch (error) {
                    resolve(false)
                }
            })
        }

        // 通过脚本启动
        async function startViaScript() {
            return new Promise((resolve) => {
                try {
                    const script = document.createElement('script')
                    script.src = 'http://localhost:3001/health?callback=proxyCallback&t=' + Date.now()
                    script.onerror = () => {
                        document.head.removeChild(script)
                        resolve(false)
                    }
                    script.onload = () => {
                        document.head.removeChild(script)
                        resolve(true)
                    }
                    
                    document.head.appendChild(script)

                    setTimeout(() => {
                        if (script.parentNode) {
                            document.head.removeChild(script)
                        }
                        resolve(false)
                    }, 3000)
                } catch (error) {
                    resolve(false)
                }
            })
        }

        // 测试连接
        async function testConnection() {
            addLog('🧪 测试代理服务器连接...')
            
            const tests = [
                { name: '健康检查', url: '/health' },
                { name: '状态查询', url: '/status' },
                { name: '自动启动接口', url: '/auto-start' }
            ]

            for (const test of tests) {
                try {
                    const response = await fetch(`http://localhost:3001${test.url}`, {
                        method: test.url === '/auto-start' ? 'POST' : 'GET',
                        headers: { 'Content-Type': 'application/json' },
                        timeout: 3000
                    })
                    
                    if (response.ok) {
                        addLog(`✅ ${test.name}: 连接成功`, 'success')
                    } else {
                        addLog(`⚠️ ${test.name}: HTTP ${response.status}`, 'warning')
                    }
                } catch (error) {
                    addLog(`❌ ${test.name}: ${error.message}`, 'error')
                }
            }
        }

        // 测试单个启动方法
        async function testMethod(method) {
            addLog(`🔧 测试${method}启动方法...`)
            
            const methods = {
                'api': startViaAPI,
                'iframe': startViaIframe,
                'worker': startViaWorker,
                'script': startViaScript
            }

            try {
                const result = await methods[method]()
                const resultDiv = document.getElementById('methodResults')
                const resultText = result ? '✅ 成功' : '❌ 失败'
                resultDiv.innerHTML += `<div>${method}启动: ${resultText}</div>`
                
                addLog(`${method}启动方法: ${result ? '成功' : '失败'}`, result ? 'success' : 'error')
            } catch (error) {
                addLog(`${method}启动方法异常: ${error.message}`, 'error')
            }
        }

        // 页面加载完成后初始化
        window.onload = function() {
            addLog('🚀 自动启动测试页面加载完成')
            updateAutoStartStatus('ready', '等待用户操作')
            
            // 自动检查状态
            checkStatus()
        }
    </script>
</body>
</html>
