<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>书籍ID提取测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .test-input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-cases {
            margin-top: 20px;
        }
        .test-case {
            margin: 10px 0;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-case:hover {
            background-color: #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 书籍ID提取功能测试</h1>
        
        <div class="test-section">
            <h3>🔍 测试书籍ID提取</h3>
            <input type="text" id="testInput" class="test-input" 
                   placeholder="请输入番茄小说链接或书籍ID进行测试">
            <button class="test-button" onclick="testExtraction()">提取书籍ID</button>
            <div id="result"></div>
        </div>

        <div class="test-section">
            <h3>📋 预设测试用例</h3>
            <p>点击下面的测试用例自动填入输入框：</p>
            <div class="test-cases">
                <div class="test-case" onclick="fillInput('https://changdunovel.com/wap/share-v2.html?aid=1967&book_id=7351352518972034110&share_type=0&share_code=S3fu6lZ1q5ccjAVeiAfF2nw0QmSC4VGmYc-4NW7gQCc%3D&uid=c180e9d58e50adc59ea18611e5e9b3cb&share_id=K7XZUZPk9czRnGyxzKHmx0Kbexw8QeFYpb1oKSdu-IY%3D&use_open_launch_app=1&user_id=40ea4a7b55f077f0e4686d544f083b12&did=c180e9d58e50adc59ea18611e5e9b3cb&entrance=reader_paragraph&zlink=https%3A%2F%2Fzlink.fqnovel.com%2FdhVGe&gd_label=click_schema_lhft_share_novelapp_android&source_channel=sys_share&share_channel=sys_share&type=book&share_timestamp=1755096592&share_token=cb572eee-20d7-42a0-a3e3-be00e0f8e596')">
                    <strong>分享链接测试：</strong><br>
                    https://changdunovel.com/wap/share-v2.html?book_id=7351352518972034110...
                    <br><small>期望结果: 7351352518972034110</small>
                </div>
                
                <div class="test-case" onclick="fillInput('https://fanqienovel.com/page/7351352518972034110')">
                    <strong>标准页面链接：</strong><br>
                    https://fanqienovel.com/page/7351352518972034110
                    <br><small>期望结果: 7351352518972034110</small>
                </div>
                
                <div class="test-case" onclick="fillInput('7351352518972034110')">
                    <strong>纯数字ID：</strong><br>
                    7351352518972034110
                    <br><small>期望结果: 7351352518972034110</small>
                </div>
                
                <div class="test-case" onclick="fillInput('https://fanqienovel.com/reader/7351352518972034110')">
                    <strong>阅读器链接：</strong><br>
                    https://fanqienovel.com/reader/7351352518972034110
                    <br><small>期望结果: 7351352518972034110</small>
                </div>
                
                <div class="test-case" onclick="fillInput('invalid-url-test')">
                    <strong>无效链接测试：</strong><br>
                    invalid-url-test
                    <br><small>期望结果: null (提取失败)</small>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 从URL提取书籍ID的函数（与Vue组件中的逻辑一致）
        function extractBookId(url) {
            if (!url) return null;

            // 检查是否为纯数字ID
            if (/^\d+$/.test(url.trim())) {
                return url.trim();
            }

            // 支持多种URL格式，包括番茄小说分享链接
            const patterns = [
                /\/page\/(\d+)/,           // /page/123456
                /\/reader\/(\d+)/,         // /reader/123456
                /[?&]book_id=(\d+)/,       // ?book_id=123456 (支持分享链接中的book_id参数)
                /[?&]bookId=(\d+)/,        // ?bookId=123456
                /(\d{19})/                 // 直接的19位数字
            ];

            for (const pattern of patterns) {
                const match = url.match(pattern);
                if (match && match[1]) {
                    return match[1];
                }
            }

            return null;
        }

        // 测试提取功能
        function testExtraction() {
            const input = document.getElementById('testInput').value;
            const result = document.getElementById('result');
            
            if (!input.trim()) {
                result.innerHTML = '<div class="error">请输入要测试的链接或ID</div>';
                return;
            }

            const bookId = extractBookId(input);
            
            if (bookId) {
                result.innerHTML = `
                    <div class="success">
                        <strong>✅ 提取成功！</strong><br>
                        输入: ${input}<br>
                        提取到的书籍ID: <strong>${bookId}</strong>
                    </div>
                `;
            } else {
                result.innerHTML = `
                    <div class="error">
                        <strong>❌ 提取失败</strong><br>
                        输入: ${input}<br>
                        无法从输入中提取有效的书籍ID
                    </div>
                `;
            }
        }

        // 填入测试用例
        function fillInput(testCase) {
            document.getElementById('testInput').value = testCase;
            // 自动执行测试
            testExtraction();
        }

        // 页面加载完成后自动测试第一个用例
        window.onload = function() {
            fillInput('https://changdunovel.com/wap/share-v2.html?aid=1967&book_id=7351352518972034110&share_type=0&share_code=S3fu6lZ1q5ccjAVeiAfF2nw0QmSC4VGmYc-4NW7gQCc%3D&uid=c180e9d58e50adc59ea18611e5e9b3cb&share_id=K7XZUZPk9czRnGyxzKHmx0Kbexw8QeFYpb1oKSdu-IY%3D&use_open_launch_app=1&user_id=40ea4a7b55f077f0e4686d544f083b12&did=c180e9d58e50adc59ea18611e5e9b3cb&entrance=reader_paragraph&zlink=https%3A%2F%2Fzlink.fqnovel.com%2FdhVGe&gd_label=click_schema_lhft_share_novelapp_android&source_channel=sys_share&share_channel=sys_share&type=book&share_timestamp=1755096592&share_token=cb572eee-20d7-42a0-a3e3-be00e0f8e596');
        };
    </script>
</body>
</html>
