#!/usr/bin/env node

/**
 * 书籍ID提取功能测试脚本
 * 测试从各种格式的URL中提取书籍ID的功能
 */

// 从URL提取书籍ID的函数（与Vue组件中的逻辑一致）
function extractBookId(url) {
  if (!url) return null;

  // 检查是否为纯数字ID
  if (/^\d+$/.test(url.trim())) {
    return url.trim();
  }

  // 支持多种URL格式，包括番茄小说分享链接
  const patterns = [
    /\/page\/(\d+)/,           // /page/123456
    /\/reader\/(\d+)/,         // /reader/123456
    /[?&]book_id=(\d+)/,       // ?book_id=123456 (支持分享链接中的book_id参数)
    /[?&]bookId=(\d+)/,        // ?bookId=123456
    /(\d{19})/                 // 直接的19位数字
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return null;
}

// 测试用例
const testCases = [
  {
    name: '分享链接测试',
    input: 'https://changdunovel.com/wap/share-v2.html?aid=1967&book_id=7351352518972034110&share_type=0&share_code=S3fu6lZ1q5ccjAVeiAfF2nw0QmSC4VGmYc-4NW7gQCc%3D&uid=c180e9d58e50adc59ea18611e5e9b3cb&share_id=K7XZUZPk9czRnGyxzKHmx0Kbexw8QeFYpb1oKSdu-IY%3D&use_open_launch_app=1&user_id=40ea4a7b55f077f0e4686d544f083b12&did=c180e9d58e50adc59ea18611e5e9b3cb&entrance=reader_paragraph&zlink=https%3A%2F%2Fzlink.fqnovel.com%2FdhVGe&gd_label=click_schema_lhft_share_novelapp_android&source_channel=sys_share&share_channel=sys_share&type=book&share_timestamp=1755096592&share_token=cb572eee-20d7-42a0-a3e3-be00e0f8e596',
    expected: '7351352518972034110'
  },
  {
    name: '标准页面链接',
    input: 'https://fanqienovel.com/page/7351352518972034110',
    expected: '7351352518972034110'
  },
  {
    name: '阅读器链接',
    input: 'https://fanqienovel.com/reader/7351352518972034110',
    expected: '7351352518972034110'
  },
  {
    name: '纯数字ID',
    input: '7351352518972034110',
    expected: '7351352518972034110'
  },
  {
    name: '带bookId参数的链接',
    input: 'https://example.com/novel?bookId=7351352518972034110',
    expected: '7351352518972034110'
  },
  {
    name: '简短的book_id参数',
    input: 'https://example.com?book_id=123456789',
    expected: '123456789'
  },
  {
    name: '无效链接测试',
    input: 'https://example.com/invalid',
    expected: null
  },
  {
    name: '空输入测试',
    input: '',
    expected: null
  },
  {
    name: '纯文本测试',
    input: 'invalid-text',
    expected: null
  }
];

// 运行测试
console.log('🧪 开始测试书籍ID提取功能...\n');

let passedTests = 0;
let totalTests = testCases.length;

testCases.forEach((testCase, index) => {
  const result = extractBookId(testCase.input);
  const passed = result === testCase.expected;
  
  console.log(`测试 ${index + 1}: ${testCase.name}`);
  console.log(`输入: ${testCase.input.length > 100 ? testCase.input.substring(0, 100) + '...' : testCase.input}`);
  console.log(`期望: ${testCase.expected}`);
  console.log(`结果: ${result}`);
  console.log(`状态: ${passed ? '✅ 通过' : '❌ 失败'}`);
  console.log('─'.repeat(80));
  
  if (passed) {
    passedTests++;
  }
});

// 测试结果汇总
console.log(`\n📊 测试结果汇总:`);
console.log(`总测试数: ${totalTests}`);
console.log(`通过测试: ${passedTests}`);
console.log(`失败测试: ${totalTests - passedTests}`);
console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 所有测试通过！书籍ID提取功能正常工作。');
  process.exit(0);
} else {
  console.log('\n⚠️ 部分测试失败，请检查代码逻辑。');
  process.exit(1);
}
