<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能代理系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-running { background-color: #67c23a; }
        .status-stopped { background-color: #f56c6c; }
        .status-fallback { background-color: #e6a23c; }
        .status-checking { 
            background-color: #409eff; 
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background-color: #fafafa;
        }
        .button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .test-input {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .device-info {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .feature-item {
            padding: 10px;
            border-radius: 6px;
            font-size: 13px;
        }
        .feature-available {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .feature-limited {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .feature-unavailable {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 智能代理系统测试</h1>
        
        <!-- 设备信息 -->
        <div class="device-info">
            <h3>📱 设备信息</h3>
            <div id="deviceInfo">检测中...</div>
        </div>

        <!-- 状态面板 -->
        <div class="status-grid">
            <div class="status-card">
                <h3>📡 代理服务器状态</h3>
                <div id="proxyStatus">
                    <span class="status-indicator status-checking"></span>
                    <span>检查中...</span>
                </div>
                <div style="margin-top: 10px; font-size: 12px; color: #666;" id="proxyDetails"></div>
            </div>
            
            <div class="status-card">
                <h3>🔄 运行模式</h3>
                <div id="modeStatus">
                    <span class="status-indicator status-checking"></span>
                    <span>检测中...</span>
                </div>
                <div style="margin-top: 10px; font-size: 12px; color: #666;" id="modeDetails"></div>
            </div>
        </div>

        <!-- 功能测试 -->
        <div class="test-section">
            <h3>🧪 功能测试</h3>
            <div style="margin-bottom: 15px;">
                <button class="button" onclick="checkAllStatus()">🔍 全面检测</button>
                <button class="button" onclick="testBookIdExtraction()">📚 测试书籍ID提取</button>
                <button class="button" onclick="testFallbackMode()">📱 测试降级模式</button>
                <button class="button" onclick="clearLogs()">🗑️ 清空日志</button>
            </div>
            
            <input type="text" id="testUrl" class="test-input" 
                   placeholder="输入番茄小说链接进行测试"
                   value="https://changdunovel.com/wap/share-v2.html?book_id=7351352518972034110">
        </div>

        <!-- 功能支持情况 -->
        <div class="test-section">
            <h3>⚡ 功能支持情况</h3>
            <div class="feature-list" id="featureList">
                <!-- 动态生成 -->
            </div>
        </div>

        <!-- 日志区域 -->
        <div class="test-section">
            <h3>📝 系统日志</h3>
            <div id="logArea" class="log-area"></div>
        </div>
    </div>

    <script>
        // 模拟智能代理管理器的核心功能
        class MockProxyManager {
            constructor() {
                this.proxyUrls = [
                    'http://localhost:3001',
                    'https://proxy.example.com',
                    'http://127.0.0.1:3001'
                ]
                this.currentProxyUrl = null
                this.isRunning = false
                this.isMobile = this.detectMobile()
                this.fallbackMode = false
            }

            detectMobile() {
                return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
            }

            async checkProxyStatus() {
                for (const proxyUrl of this.proxyUrls) {
                    const isWorking = await this.testProxyUrl(proxyUrl)
                    if (isWorking) {
                        this.currentProxyUrl = proxyUrl
                        this.isRunning = true
                        this.fallbackMode = false
                        return true
                    }
                }
                
                this.isRunning = false
                this.currentProxyUrl = null
                
                // 移动设备自动启用降级模式
                if (this.isMobile) {
                    this.fallbackMode = true
                    addLog('📱 移动设备自动启用降级模式', 'info')
                }
                
                return false
            }

            async testProxyUrl(proxyUrl) {
                try {
                    const response = await fetch(`${proxyUrl}/health`, {
                        method: 'GET',
                        timeout: 3000
                    })
                    return response.ok
                } catch {
                    return false
                }
            }

            getStatus() {
                return {
                    isRunning: this.isRunning,
                    currentProxyUrl: this.currentProxyUrl,
                    isMobile: this.isMobile,
                    fallbackMode: this.fallbackMode,
                    availableProxies: this.proxyUrls
                }
            }
        }

        // 模拟降级解析器
        class MockFallbackParser {
            parseBookInfo(bookId) {
                return {
                    success: true,
                    data: {
                        novelId: bookId,
                        novelName: `小说_${bookId}`,
                        author: '未知作者',
                        description: '暂无简介（降级模式）',
                        parseMethod: 'fallback'
                    },
                    method: 'fallback'
                }
            }
        }

        // 全局实例
        const proxyManager = new MockProxyManager()
        const fallbackParser = new MockFallbackParser()
        let logs = []

        // 添加日志
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString()
            const logEntry = `[${timestamp}] ${message}`
            logs.push(logEntry)
            
            const logArea = document.getElementById('logArea')
            const logElement = document.createElement('div')
            logElement.textContent = logEntry
            logElement.style.color = type === 'error' ? '#f56c6c' : 
                                   type === 'success' ? '#67c23a' : 
                                   type === 'warning' ? '#e6a23c' : '#333'
            logArea.appendChild(logElement)
            logArea.scrollTop = logArea.scrollHeight
        }

        // 清空日志
        function clearLogs() {
            logs = []
            document.getElementById('logArea').innerHTML = ''
        }

        // 更新设备信息
        function updateDeviceInfo() {
            const deviceInfo = document.getElementById('deviceInfo')
            const isMobile = proxyManager.isMobile
            const userAgent = navigator.userAgent
            
            deviceInfo.innerHTML = `
                <strong>设备类型:</strong> ${isMobile ? '📱 移动设备' : '🖥️ 桌面设备'}<br>
                <strong>浏览器:</strong> ${getBrowserName()}<br>
                <strong>屏幕尺寸:</strong> ${window.screen.width}x${window.screen.height}<br>
                <strong>视口尺寸:</strong> ${window.innerWidth}x${window.innerHeight}
            `
        }

        // 获取浏览器名称
        function getBrowserName() {
            const ua = navigator.userAgent
            if (ua.includes('Chrome')) return 'Chrome'
            if (ua.includes('Firefox')) return 'Firefox'
            if (ua.includes('Safari')) return 'Safari'
            if (ua.includes('Edge')) return 'Edge'
            return '未知浏览器'
        }

        // 检查所有状态
        async function checkAllStatus() {
            addLog('🔍 开始全面检测...')
            
            // 检查代理状态
            const isRunning = await proxyManager.checkProxyStatus()
            const status = proxyManager.getStatus()
            
            updateProxyStatus(status)
            updateModeStatus(status)
            updateFeatureList(status)
            
            addLog(`检测完成: ${isRunning ? '代理可用' : '代理不可用'}`, isRunning ? 'success' : 'warning')
        }

        // 更新代理状态显示
        function updateProxyStatus(status) {
            const proxyStatus = document.getElementById('proxyStatus')
            const proxyDetails = document.getElementById('proxyDetails')
            
            if (status.isRunning) {
                proxyStatus.innerHTML = '<span class="status-indicator status-running"></span><span>运行中 ✅</span>'
                proxyDetails.textContent = `当前代理: ${status.currentProxyUrl}`
            } else {
                proxyStatus.innerHTML = '<span class="status-indicator status-stopped"></span><span>未运行 ❌</span>'
                proxyDetails.textContent = '所有代理服务器都不可用'
            }
        }

        // 更新模式状态显示
        function updateModeStatus(status) {
            const modeStatus = document.getElementById('modeStatus')
            const modeDetails = document.getElementById('modeDetails')
            
            if (status.fallbackMode) {
                modeStatus.innerHTML = '<span class="status-indicator status-fallback"></span><span>降级模式 📱</span>'
                modeDetails.textContent = '功能受限但基本可用'
            } else if (status.isRunning) {
                modeStatus.innerHTML = '<span class="status-indicator status-running"></span><span>完整模式 🚀</span>'
                modeDetails.textContent = '所有功能可用'
            } else {
                modeStatus.innerHTML = '<span class="status-indicator status-stopped"></span><span>等待启动 ⏳</span>'
                modeDetails.textContent = '需要启动代理服务器'
            }
        }

        // 更新功能列表
        function updateFeatureList(status) {
            const featureList = document.getElementById('featureList')
            
            const features = [
                {
                    name: '📚 书籍ID提取',
                    available: true,
                    description: '从各种链接格式提取书籍ID'
                },
                {
                    name: '🔍 书籍信息解析',
                    available: status.isRunning,
                    description: '自动获取书名、作者、简介'
                },
                {
                    name: '📱 降级模式',
                    available: status.fallbackMode || status.isMobile,
                    description: '基础功能，手动输入信息'
                },
                {
                    name: '📥 下载功能',
                    available: true,
                    description: '不依赖代理，始终可用'
                }
            ]
            
            featureList.innerHTML = features.map(feature => `
                <div class="feature-item ${feature.available ? 'feature-available' : 'feature-unavailable'}">
                    <strong>${feature.name}</strong><br>
                    <small>${feature.description}</small><br>
                    <span style="font-size: 11px;">${feature.available ? '✅ 可用' : '❌ 不可用'}</span>
                </div>
            `).join('')
        }

        // 测试书籍ID提取
        function testBookIdExtraction() {
            const url = document.getElementById('testUrl').value
            addLog(`📚 测试书籍ID提取: ${url}`)
            
            // 模拟提取逻辑
            const patterns = [
                /[?&]book_id=(\d+)/,
                /\/page\/(\d+)/,
                /\/reader\/(\d+)/,
                /(\d{19})/
            ]
            
            let bookId = null
            for (const pattern of patterns) {
                const match = url.match(pattern)
                if (match && match[1]) {
                    bookId = match[1]
                    break
                }
            }
            
            if (bookId) {
                addLog(`✅ 提取成功: ${bookId}`, 'success')
            } else {
                addLog('❌ 提取失败: 无法识别的URL格式', 'error')
            }
        }

        // 测试降级模式
        function testFallbackMode() {
            addLog('📱 测试降级模式...')
            
            const url = document.getElementById('testUrl').value
            const bookId = '7351352518972034110' // 模拟提取的ID
            
            const result = fallbackParser.parseBookInfo(bookId)
            
            if (result.success) {
                addLog(`✅ 降级模式解析成功:`, 'success')
                addLog(`   书籍ID: ${result.data.novelId}`)
                addLog(`   书名: ${result.data.novelName}`)
                addLog(`   作者: ${result.data.author}`)
                addLog(`   解析方式: ${result.data.parseMethod}`)
            } else {
                addLog('❌ 降级模式解析失败', 'error')
            }
        }

        // 页面加载完成后初始化
        window.onload = function() {
            addLog('🚀 智能代理系统测试页面加载完成')
            updateDeviceInfo()
            checkAllStatus()
        }
    </script>
</body>
</html>
