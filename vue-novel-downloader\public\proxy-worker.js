/**
 * 代理服务器 Service Worker
 * 用于在后台自动启动和管理代理服务器
 */

const PROXY_PORT = 3001
const PROXY_URL = `http://localhost:${PROXY_PORT}`

// Service Worker安装
self.addEventListener('install', (event) => {
  console.log('📦 代理Service Worker安装中...')
  self.skipWaiting()
})

// Service Worker激活
self.addEventListener('activate', (event) => {
  console.log('✅ 代理Service Worker已激活')
  event.waitUntil(self.clients.claim())
})

// 监听消息
self.addEventListener('message', (event) => {
  const { action, port } = event.data
  
  if (action === 'startProxy') {
    handleStartProxy(event, port || PROXY_PORT)
  } else if (action === 'checkProxy') {
    handleCheckProxy(event)
  }
})

/**
 * 处理启动代理服务器请求
 */
async function handleStartProxy(event, port) {
  try {
    console.log(`🚀 Service Worker尝试启动代理服务器 (端口: ${port})`)
    
    // 尝试多种启动方法
    const methods = [
      () => startViaFetch(port),
      () => startViaWebSocket(port),
      () => startViaBeacon(port)
    ]
    
    let success = false
    
    for (const method of methods) {
      try {
        const result = await method()
        if (result) {
          success = true
          break
        }
      } catch (error) {
        console.warn('启动方法失败:', error.message)
        continue
      }
    }
    
    // 验证启动结果
    if (success) {
      // 等待一段时间后验证
      await new Promise(resolve => setTimeout(resolve, 2000))
      success = await checkProxyHealth(port)
    }
    
    // 发送结果
    event.ports[0].postMessage({
      success: success,
      message: success ? '代理服务器启动成功' : '代理服务器启动失败'
    })
    
  } catch (error) {
    console.error('❌ Service Worker启动代理失败:', error)
    event.ports[0].postMessage({
      success: false,
      error: error.message
    })
  }
}

/**
 * 处理检查代理服务器请求
 */
async function handleCheckProxy(event) {
  try {
    const isRunning = await checkProxyHealth(PROXY_PORT)
    event.ports[0].postMessage({
      success: true,
      isRunning: isRunning
    })
  } catch (error) {
    event.ports[0].postMessage({
      success: false,
      error: error.message
    })
  }
}

/**
 * 通过Fetch API启动
 */
async function startViaFetch(port) {
  try {
    const response = await fetch(`http://localhost:${port}/auto-start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      mode: 'no-cors'
    })
    
    return true // no-cors模式下无法检查响应状态，假设成功
  } catch (error) {
    return false
  }
}

/**
 * 通过WebSocket启动
 */
async function startViaWebSocket(port) {
  return new Promise((resolve) => {
    try {
      const ws = new WebSocket(`ws://localhost:${port}`)
      
      ws.onopen = () => {
        console.log('WebSocket连接成功，可能触发了代理启动')
        ws.close()
        resolve(true)
      }
      
      ws.onerror = () => {
        resolve(false)
      }
      
      // 超时处理
      setTimeout(() => {
        ws.close()
        resolve(false)
      }, 3000)
      
    } catch (error) {
      resolve(false)
    }
  })
}

/**
 * 通过Beacon API启动
 */
async function startViaBeacon(port) {
  try {
    if ('sendBeacon' in navigator) {
      const data = JSON.stringify({ action: 'start', port: port })
      const success = navigator.sendBeacon(`http://localhost:${port}/auto-start`, data)
      return success
    }
    return false
  } catch (error) {
    return false
  }
}

/**
 * 检查代理服务器健康状态
 */
async function checkProxyHealth(port) {
  try {
    const response = await fetch(`http://localhost:${port}/health`, {
      method: 'GET',
      mode: 'no-cors'
    })
    
    // 在no-cors模式下，我们无法检查响应内容
    // 但如果请求没有抛出异常，说明服务器可能在运行
    return true
  } catch (error) {
    return false
  }
}

/**
 * 定期检查代理服务器状态
 */
function startPeriodicCheck() {
  setInterval(async () => {
    const isRunning = await checkProxyHealth(PROXY_PORT)
    
    // 向所有客户端广播状态
    const clients = await self.clients.matchAll()
    clients.forEach(client => {
      client.postMessage({
        type: 'proxy-status',
        isRunning: isRunning,
        timestamp: Date.now()
      })
    })
  }, 30000) // 每30秒检查一次
}

// 启动定期检查
startPeriodicCheck()

// 处理fetch事件（可选：代理请求）
self.addEventListener('fetch', (event) => {
  const url = new URL(event.request.url)
  
  // 如果是代理相关请求，可以在这里处理
  if (url.hostname === 'localhost' && url.port === PROXY_PORT.toString()) {
    // 可以在这里添加代理逻辑
    return
  }
  
  // 其他请求正常处理
  event.respondWith(fetch(event.request))
})
