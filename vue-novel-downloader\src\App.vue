<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
import proxyManager from '@/utils/proxy-manager'

export default {
  name: 'App',
  data() {
    return {
      proxyStatus: {
        isChecking: false,
        isRunning: false,
        showNotification: false
      }
    }
  },
  async created() {
    // 初始化代理服务器
    await this.initializeProxy()

    // 检查用户激活状态并自动跳转
    try {
      const isActivated = await this.$store.dispatch('user/checkActivation')

      if (isActivated) {
        // 如果已激活且当前在激活页面，自动跳转到主页
        if (this.$route.path === '/activation') {
          this.$router.replace('/fanqie').catch(err => {
            // 忽略重复导航错误
            if (err.name !== 'NavigationDuplicated') {
              console.error('路由跳转失败:', err)
            }
          })
        }
      } else {
        // 如果未激活且不在激活页面，跳转到激活页面
        if (this.$route.path !== '/activation') {
          this.$router.replace('/activation').catch(err => {
            // 忽略重复导航错误
            if (err.name !== 'NavigationDuplicated') {
              console.error('路由跳转失败:', err)
            }
          })
        }
      }
    } catch (error) {
      console.error('检查激活状态失败:', error)
    }
  },
  methods: {
    /**
     * 初始化代理服务器
     */
    async initializeProxy() {
      try {
        this.proxyStatus.isChecking = true

        console.log('🔍 检查代理服务器状态...')

        // 检查代理服务器是否运行
        const isRunning = await proxyManager.checkProxyStatus()

        if (isRunning) {
          console.log('✅ 代理服务器已运行，书籍解析功能可用')
          this.proxyStatus.isRunning = true
        } else {
          console.log('⚠️ 代理服务器未运行，正在尝试启动...')
          this.showProxyNotification()

          // 尝试启动代理服务器
          await proxyManager.startProxyServer()
        }

        // 监听代理服务器相关事件
        window.addEventListener('proxy-server-ready', this.onProxyReady)
        window.addEventListener('proxy-server-needed', this.onProxyNeeded)
        window.addEventListener('proxy-fallback-mode', this.onFallbackMode)

      } catch (error) {
        console.error('❌ 初始化代理服务器失败:', error)
      } finally {
        this.proxyStatus.isChecking = false
      }
    },

    /**
     * 显示代理服务器通知
     */
    showProxyNotification() {
      if (this.proxyStatus.showNotification) return

      this.proxyStatus.showNotification = true

      this.$notify({
        title: '代理服务器提醒',
        message: '为了使用书籍解析功能，需要启动代理服务器。点击查看启动方法。',
        type: 'warning',
        duration: 0, // 不自动关闭
        showClose: true,
        onClick: () => {
          this.openProxyInstructions()
        }
      })
    },

    /**
     * 处理需要代理服务器事件
     */
    onProxyNeeded(event) {
      console.log('📢 收到代理服务器需求事件')

      this.$confirm(
        `<div style="text-align: left; line-height: 1.8;">
          <p><strong>📡 需要启动代理服务器</strong></p>
          <p>书籍解析功能需要代理服务器支持，请按以下步骤启动：</p>
          <ol style="margin: 15px 0; padding-left: 20px;">
            <li><strong>推荐方法：</strong>双击项目根目录下的 <code>start-proxy.bat</code> 文件</li>
            <li><strong>命令行方法：</strong>在项目根目录运行 <code>node proxy-server.js</code></li>
          </ol>
          <p style="color: #e6a23c;"><strong>💡 提示：</strong>启动后系统会自动检测并提示成功</p>
        </div>`,
        '代理服务器启动指南',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '我已启动',
          cancelButtonText: '稍后启动',
          type: 'warning'
        }
      ).then(() => {
        // 用户点击"我已启动"，重新检查状态
        this.recheckProxyStatus()
      }).catch(() => {
        // 用户选择稍后启动
        console.log('用户选择稍后启动代理服务器')
      })
    },

    /**
     * 重新检查代理服务器状态
     */
    async recheckProxyStatus() {
      try {
        console.log('🔄 重新检查代理服务器状态...')
        const isRunning = await proxyManager.checkProxyStatus()

        if (isRunning) {
          this.onProxyReady()
        } else {
          this.$message.warning('代理服务器仍未运行，请确保已正确启动')
          // 继续监听
          setTimeout(() => {
            this.recheckProxyStatus()
          }, 3000)
        }
      } catch (error) {
        console.error('重新检查代理服务器状态失败:', error)
        this.$message.error('检查代理服务器状态失败')
      }
    },

    /**
     * 处理降级模式事件
     */
    onFallbackMode(event) {
      console.log('📱 进入降级模式')

      const detail = event.detail || {}

      this.$notify({
        title: '降级模式已启用',
        message: detail.message || '代理服务器不可用，已启用降级模式。功能可能受限但基本可用。',
        type: 'info',
        duration: 6000,
        showClose: true
      })

      // 如果是移动设备，显示特殊提示
      if (proxyManager.isMobile) {
        this.$message({
          message: '📱 移动设备已启用降级模式，部分功能可能受限',
          type: 'info',
          duration: 4000
        })
      }
    },

    /**
     * 代理服务器就绪回调
     */
    onProxyReady() {
      console.log('🎉 代理服务器已就绪')
      this.proxyStatus.isRunning = true

      this.$notify({
        title: '代理服务器已启动',
        message: '书籍解析功能现在可以正常使用了！',
        type: 'success',
        duration: 3000
      })
    },

    /**
     * 打开代理服务器说明
     */
    openProxyInstructions() {
      this.$alert(`
        <div style="text-align: left; line-height: 1.6;">
          <h4>启动代理服务器的方法：</h4>
          <p><strong>方法1（推荐）：</strong></p>
          <p>双击项目根目录下的 <code>start-proxy.bat</code> 文件</p>
          <br>
          <p><strong>方法2：</strong></p>
          <p>在项目根目录打开命令行，运行：</p>
          <p><code>node proxy-server.js</code></p>
          <br>
          <p><strong>注意：</strong></p>
          <p>• 代理服务器运行在端口 3001</p>
          <p>• 启动后会自动检测并提示</p>
          <p>• 关闭浏览器不会影响代理服务器运行</p>
        </div>
      `, '代理服务器启动说明', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '我知道了'
      })
    }
  },

  beforeDestroy() {
    // 清理事件监听器
    window.removeEventListener('proxy-server-ready', this.onProxyReady)
    window.removeEventListener('proxy-server-needed', this.onProxyNeeded)
    window.removeEventListener('proxy-fallback-mode', this.onFallbackMode)

    // 清理代理管理器
    if (proxyManager) {
      proxyManager.destroy()
    }
  }
}
</script>

<style lang="scss">
// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
  min-height: 100vh;
}

// Element UI 样式覆盖
.el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s;

  &--primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #5a6fd8, #6a4190);
      transform: translateY(-1px);
    }
  }

  &--large {
    padding: 12px 24px;
    font-size: 16px;
  }
}

.el-card {
  border-radius: 16px;
  border: none;
  overflow: hidden;

  .el-card__header {
    background: rgba(255, 255, 255, 0.8);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }
}

.el-input {
  .el-input__inner {
    border-radius: 8px;
    border: 1px solid #e0e6ed;
    transition: all 0.3s;

    &:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
    }
  }

  &--large .el-input__inner {
    height: 48px;
    font-size: 16px;
  }
}

.el-progress {
  .el-progress-bar__outer {
    border-radius: 12px;
    background: rgba(0, 0, 0, 0.1);
  }

  .el-progress-bar__inner {
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea, #764ba2);
  }
}

.el-checkbox {
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #667eea;
    border-color: #667eea;
  }
}

.el-radio-button {
  .el-radio-button__inner {
    border-radius: 6px;
    border: 1px solid #ddd;
    background: white;
    color: #666;
    transition: all 0.3s;

    &:hover {
      color: #667eea;
      border-color: #667eea;
    }
  }

  &.is-active .el-radio-button__inner {
    background: #667eea;
    border-color: #667eea;
    color: white;
  }
}

// 自定义滚动条
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 动画效果
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.3s ease;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter, .slide-fade-leave-to {
  transform: translateX(10px);
  opacity: 0;
}
</style>
