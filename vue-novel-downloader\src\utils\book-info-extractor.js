/**
 * 书籍信息提取器
 * 使用正则表达式和DOM解析从HTML中提取书籍信息
 */

class BookInfoExtractor {
  constructor() {
    // 书名提取规则
    this.titleSelectors = [
      'h1',
      '.book-title',
      '.novel-title',
      '[class*="title"]',
      'title'
    ]
    
    this.titlePatterns = [
      /<h1[^>]*>(.*?)<\/h1>/i,
      /<title[^>]*>(.*?)<\/title>/i,
      /class="[^"]*title[^"]*"[^>]*>(.*?)</i
    ]
    
    // 作者提取规则
    this.authorSelectors = [
      '.author-name-text',
      '.author-name',
      '.book-author',
      '[class*="author"]',
      '.info-author'
    ]
    
    this.authorPatterns = [
      /作者[：:]\s*([^<\n]+)/i,
      /author[：:]\s*([^<\n]+)/i,
      /class="[^"]*author[^"]*"[^>]*>([^<]+)</i
    ]
    
    // 简介提取规则
    this.descSelectors = [
      '.page-abstract-content',
      '.book-intro',
      '.description',
      '.book-desc',
      '[class*="abstract"]',
      '[class*="intro"]',
      '[class*="desc"]'
    ]
    
    this.descPatterns = [
      /class="[^"]*abstract[^"]*"[^>]*>(.*?)</i,
      /class="[^"]*intro[^"]*"[^>]*>(.*?)</i,
      /简介[：:]\s*(.*?)(?=<|$)/i
    ]
  }

  /**
   * 从HTML中提取书籍信息
   */
  extractBookInfo(html, bookId) {
    try {
      // 清理HTML
      const cleanHtml = this.cleanHtml(html)
      
      // 创建DOM解析器
      const parser = new DOMParser()
      const doc = parser.parseFromString(cleanHtml, 'text/html')
      
      // 提取各项信息
      const novelName = this.extractTitle(doc, cleanHtml)
      const author = this.extractAuthor(doc, cleanHtml)
      const description = this.extractDescription(doc, cleanHtml)
      const coverUrl = this.extractCover(doc)
      
      return {
        novelId: bookId,
        novelName: novelName || `小说_${bookId}`,
        author: author || '未知作者',
        description: description || '暂无简介',
        coverUrl: coverUrl || '',
        totalChapters: 0,
        parseTime: new Date().toISOString(),
        source: 'html_extraction',
        confidence: this.calculateConfidence(novelName, author, description)
      }
      
    } catch (error) {
      console.error('HTML解析失败:', error)
      return this.getFallbackInfo(bookId)
    }
  }

  /**
   * 清理HTML内容
   */
  cleanHtml(html) {
    return html
      .replace(/<!--[\s\S]*?-->/g, '') // 移除注释
      .replace(/<script[\s\S]*?<\/script>/gi, '') // 移除脚本
      .replace(/<style[\s\S]*?<\/style>/gi, '') // 移除样式
      .replace(/\s+/g, ' ') // 压缩空白字符
      .trim()
  }

  /**
   * 提取书名
   */
  extractTitle(doc, html) {
    // 方法1: 使用CSS选择器
    for (const selector of this.titleSelectors) {
      const element = doc.querySelector(selector)
      if (element && element.textContent.trim()) {
        const title = element.textContent.trim()
        // 过滤掉明显不是书名的内容
        if (this.isValidTitle(title)) {
          return this.cleanTitle(title)
        }
      }
    }
    
    // 方法2: 使用正则表达式
    for (const pattern of this.titlePatterns) {
      const match = html.match(pattern)
      if (match && match[1]) {
        const title = this.stripHtml(match[1]).trim()
        if (this.isValidTitle(title)) {
          return this.cleanTitle(title)
        }
      }
    }
    
    return null
  }

  /**
   * 提取作者
   */
  extractAuthor(doc, html) {
    // 方法1: 使用CSS选择器
    for (const selector of this.authorSelectors) {
      const element = doc.querySelector(selector)
      if (element && element.textContent.trim()) {
        const author = element.textContent.trim()
        if (this.isValidAuthor(author)) {
          return this.cleanAuthor(author)
        }
      }
    }
    
    // 方法2: 使用正则表达式
    for (const pattern of this.authorPatterns) {
      const match = html.match(pattern)
      if (match && match[1]) {
        const author = this.stripHtml(match[1]).trim()
        if (this.isValidAuthor(author)) {
          return this.cleanAuthor(author)
        }
      }
    }
    
    return null
  }

  /**
   * 提取简介
   */
  extractDescription(doc, html) {
    // 方法1: 使用CSS选择器
    for (const selector of this.descSelectors) {
      const element = doc.querySelector(selector)
      if (element && element.textContent.trim()) {
        const desc = element.textContent.trim()
        if (this.isValidDescription(desc)) {
          return this.cleanDescription(desc)
        }
      }
    }
    
    // 方法2: 使用正则表达式
    for (const pattern of this.descPatterns) {
      const match = html.match(pattern)
      if (match && match[1]) {
        const desc = this.stripHtml(match[1]).trim()
        if (this.isValidDescription(desc)) {
          return this.cleanDescription(desc)
        }
      }
    }
    
    return null
  }

  /**
   * 提取封面图片
   */
  extractCover(doc) {
    const coverSelectors = [
      'img[class*="cover"]',
      '.book-cover img',
      '.cover-img',
      'img[alt*="封面"]',
      'img[src*="cover"]'
    ]
    
    for (const selector of coverSelectors) {
      const img = doc.querySelector(selector)
      if (img) {
        return img.src || img.getAttribute('data-src') || img.getAttribute('data-original') || ''
      }
    }
    
    return ''
  }

  /**
   * 验证书名是否有效
   */
  isValidTitle(title) {
    if (!title || title.length < 2 || title.length > 100) return false
    
    // 排除明显不是书名的内容
    const invalidPatterns = [
      /^(首页|主页|登录|注册|搜索)$/i,
      /^(番茄小说|起点中文网|晋江文学城)$/i,
      /^(第\d+章|章节|目录)$/i,
      /^(下载|阅读|收藏)$/i
    ]
    
    return !invalidPatterns.some(pattern => pattern.test(title))
  }

  /**
   * 验证作者是否有效
   */
  isValidAuthor(author) {
    if (!author || author.length < 2 || author.length > 50) return false
    
    // 排除明显不是作者的内容
    const invalidPatterns = [
      /^(作者|author)$/i,
      /^(未知|unknown)$/i,
      /^\d+$/,
      /^(点击|查看|更多)$/i
    ]
    
    return !invalidPatterns.some(pattern => pattern.test(author))
  }

  /**
   * 验证简介是否有效
   */
  isValidDescription(desc) {
    if (!desc || desc.length < 10 || desc.length > 2000) return false
    
    // 排除明显不是简介的内容
    const invalidPatterns = [
      /^(简介|description|内容简介)$/i,
      /^(暂无|无|没有)$/i,
      /^(点击|查看|展开)$/i
    ]
    
    return !invalidPatterns.some(pattern => pattern.test(desc))
  }

  /**
   * 清理书名
   */
  cleanTitle(title) {
    return title
      .replace(/^《|》$/g, '') // 移除书名号
      .replace(/\s*-\s*番茄小说$/i, '') // 移除网站后缀
      .replace(/\s*\|\s*.*$/i, '') // 移除管道符后的内容
      .trim()
  }

  /**
   * 清理作者名
   */
  cleanAuthor(author) {
    return author
      .replace(/^(作者[：:]?\s*)/i, '') // 移除"作者:"前缀
      .replace(/\s*著$/i, '') // 移除"著"后缀
      .trim()
  }

  /**
   * 清理简介
   */
  cleanDescription(desc) {
    return desc
      .replace(/^(简介[：:]?\s*|内容简介[：:]?\s*)/i, '') // 移除简介前缀
      .replace(/\s*展开$|收起$/i, '') // 移除展开/收起
      .replace(/\s+/g, ' ') // 压缩空白
      .trim()
  }

  /**
   * 移除HTML标签
   */
  stripHtml(html) {
    return html.replace(/<[^>]*>/g, '').replace(/&[^;]+;/g, ' ')
  }

  /**
   * 计算提取信息的置信度
   */
  calculateConfidence(title, author, description) {
    let confidence = 0
    
    if (title && title !== `小说_${title}`) confidence += 40
    if (author && author !== '未知作者') confidence += 30
    if (description && description !== '暂无简介') confidence += 30
    
    return Math.min(confidence, 100)
  }

  /**
   * 获取降级信息
   */
  getFallbackInfo(bookId) {
    return {
      novelId: bookId,
      novelName: `小说_${bookId}`,
      author: '未知作者',
      description: '暂无简介',
      coverUrl: '',
      totalChapters: 0,
      parseTime: new Date().toISOString(),
      source: 'fallback',
      confidence: 0
    }
  }
}

// 创建全局实例
const bookInfoExtractor = new BookInfoExtractor()

export default bookInfoExtractor
