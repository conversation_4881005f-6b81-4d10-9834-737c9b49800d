/**
 * 增强的代理服务器管理器
 * 支持本地代理、云端代理、浏览器扩展等多种跨域解决方案
 */

import proxyManager from './proxy-manager'

class EnhancedProxyManager {
  constructor() {
    this.localProxyManager = proxyManager
    
    // 云端代理配置
    this.cloudProxies = [
      {
        name: 'AllOrigins',
        url: 'https://api.allorigins.win',
        type: 'allorigins',
        available: true,
        priority: 1,
        rateLimit: 200, // 每小时请求限制
        currentRequests: 0,
        lastReset: Date.now()
      },
      {
        name: 'CORS Anywhere',
        url: 'https://cors-anywhere.herokuapp.com',
        type: 'cors-anywhere',
        available: true,
        priority: 2,
        rateLimit: 50,
        currentRequests: 0,
        lastReset: Date.now()
      },
      {
        name: 'ThingProxy',
        url: 'https://thingproxy.freeboard.io',
        type: 'thingproxy',
        available: true,
        priority: 3,
        rateLimit: 100,
        currentRequests: 0,
        lastReset: Date.now()
      },
      {
        name: 'Proxy6',
        url: 'https://api.codetabs.com',
        type: 'codetabs',
        available: true,
        priority: 4,
        rateLimit: 100,
        currentRequests: 0,
        lastReset: Date.now()
      }
    ]
    
    // 浏览器扩展检测
    this.extensionAvailable = false
    this.checkExtensionAvailability()
    
    // 性能统计
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      proxyUsage: {}
    }
  }

  /**
   * 检查浏览器扩展是否可用
   */
  async checkExtensionAvailability() {
    try {
      if (window.chrome && window.chrome.runtime) {
        // 尝试与扩展通信
        window.chrome.runtime.sendMessage('novel-parser-extension', 
          { action: 'ping' }, 
          (response) => {
            this.extensionAvailable = !!response
            console.log(this.extensionAvailable ? '✅ 浏览器扩展可用' : '⚠️ 浏览器扩展不可用')
          }
        )
      }
    } catch (error) {
      this.extensionAvailable = false
    }
  }

  /**
   * 智能选择最佳代理方案
   */
  async getBestProxy() {
    const strategies = [
      {
        name: 'local',
        check: () => this.localProxyManager.checkProxyStatus(),
        priority: 0
      },
      {
        name: 'extension',
        check: () => Promise.resolve(this.extensionAvailable),
        priority: 1
      },
      {
        name: 'cloud',
        check: () => this.checkCloudProxiesAvailability(),
        priority: 2
      }
    ]

    // 按优先级排序
    strategies.sort((a, b) => a.priority - b.priority)

    for (const strategy of strategies) {
      try {
        const available = await strategy.check()
        if (available) {
          console.log(`🎯 选择代理策略: ${strategy.name}`)
          return strategy.name
        }
      } catch (error) {
        console.warn(`代理策略 ${strategy.name} 检查失败:`, error)
      }
    }

    return 'fallback'
  }

  /**
   * 检查云端代理可用性
   */
  async checkCloudProxiesAvailability() {
    const availableProxies = this.cloudProxies.filter(proxy => 
      proxy.available && this.checkRateLimit(proxy)
    )
    return availableProxies.length > 0
  }

  /**
   * 检查代理速率限制
   */
  checkRateLimit(proxy) {
    const now = Date.now()
    const hoursPassed = (now - proxy.lastReset) / (1000 * 60 * 60)
    
    if (hoursPassed >= 1) {
      proxy.currentRequests = 0
      proxy.lastReset = now
    }
    
    return proxy.currentRequests < proxy.rateLimit
  }

  /**
   * 智能获取页面内容
   */
  async fetchPageContent(url) {
    const startTime = Date.now()
    this.stats.totalRequests++

    try {
      const strategy = await this.getBestProxy()
      let result

      switch (strategy) {
        case 'local':
          result = await this.fetchViaLocalProxy(url)
          break
        case 'extension':
          result = await this.fetchViaExtension(url)
          break
        case 'cloud':
          result = await this.fetchViaCloudProxy(url)
          break
        default:
          throw new Error('所有代理方案都不可用')
      }

      // 更新统计信息
      const responseTime = Date.now() - startTime
      this.updateStats(true, responseTime, strategy)

      return result
    } catch (error) {
      this.updateStats(false, Date.now() - startTime)
      throw error
    }
  }

  /**
   * 通过本地代理获取内容
   */
  async fetchViaLocalProxy(url) {
    return await this.localProxyManager.fetchPageContent(url)
  }

  /**
   * 通过浏览器扩展获取内容
   */
  async fetchViaExtension(url) {
    return new Promise((resolve, reject) => {
      if (!this.extensionAvailable) {
        reject(new Error('浏览器扩展不可用'))
        return
      }

      window.chrome.runtime.sendMessage('novel-parser-extension', {
        action: 'fetchPage',
        url: url
      }, (response) => {
        if (response && response.success) {
          resolve({
            success: true,
            html: response.html,
            method: 'extension'
          })
        } else {
          reject(new Error(response?.error || '扩展请求失败'))
        }
      })
    })
  }

  /**
   * 通过云端代理获取内容
   */
  async fetchViaCloudProxy(url) {
    // 按优先级和可用性排序
    const availableProxies = this.cloudProxies
      .filter(proxy => proxy.available && this.checkRateLimit(proxy))
      .sort((a, b) => a.priority - b.priority)

    if (availableProxies.length === 0) {
      throw new Error('没有可用的云端代理')
    }

    let lastError
    for (const proxy of availableProxies) {
      try {
        console.log(`🌐 尝试云端代理: ${proxy.name}`)
        const result = await this.tryCloudProxy(url, proxy)
        
        // 更新请求计数
        proxy.currentRequests++
        this.updateProxyStats(proxy.name, true)
        
        return {
          ...result,
          method: 'cloud',
          proxy: proxy.name
        }
      } catch (error) {
        console.warn(`⚠️ 云端代理 ${proxy.name} 失败:`, error.message)
        lastError = error
        
        // 标记代理暂时不可用
        if (error.message.includes('429') || error.message.includes('rate limit')) {
          proxy.available = false
          setTimeout(() => { proxy.available = true }, 60000) // 1分钟后重试
        }
        
        this.updateProxyStats(proxy.name, false)
        continue
      }
    }

    throw lastError || new Error('所有云端代理都失败了')
  }

  /**
   * 尝试特定的云端代理
   */
  async tryCloudProxy(targetUrl, proxy) {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 15000) // 15秒超时

    try {
      let response
      
      switch (proxy.type) {
        case 'allorigins':
          response = await fetch(`${proxy.url}/get?url=${encodeURIComponent(targetUrl)}`, {
            signal: controller.signal
          })
          break
          
        case 'cors-anywhere':
          response = await fetch(`${proxy.url}/${targetUrl}`, {
            headers: { 'X-Requested-With': 'XMLHttpRequest' },
            signal: controller.signal
          })
          break
          
        case 'thingproxy':
          response = await fetch(`${proxy.url}/fetch/${targetUrl}`, {
            signal: controller.signal
          })
          break
          
        case 'codetabs':
          response = await fetch(`${proxy.url}/v1/proxy?quest=${encodeURIComponent(targetUrl)}`, {
            signal: controller.signal
          })
          break
          
        default:
          throw new Error(`未知的代理类型: ${proxy.type}`)
      }

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      let html
      if (proxy.type === 'allorigins') {
        const data = await response.json()
        html = data.contents
      } else {
        html = await response.text()
      }

      return {
        success: true,
        html: html,
        status: response.status
      }
    } catch (error) {
      clearTimeout(timeoutId)
      throw error
    }
  }

  /**
   * 更新统计信息
   */
  updateStats(success, responseTime, method = 'unknown') {
    if (success) {
      this.stats.successfulRequests++
    } else {
      this.stats.failedRequests++
    }

    // 更新平均响应时间
    const totalRequests = this.stats.successfulRequests + this.stats.failedRequests
    this.stats.averageResponseTime = (
      (this.stats.averageResponseTime * (totalRequests - 1) + responseTime) / totalRequests
    )
  }

  /**
   * 更新代理使用统计
   */
  updateProxyStats(proxyName, success) {
    if (!this.stats.proxyUsage[proxyName]) {
      this.stats.proxyUsage[proxyName] = { success: 0, failed: 0 }
    }
    
    if (success) {
      this.stats.proxyUsage[proxyName].success++
    } else {
      this.stats.proxyUsage[proxyName].failed++
    }
  }

  /**
   * 获取代理状态报告
   */
  getStatusReport() {
    return {
      localProxy: this.localProxyManager.getStatus(),
      extensionAvailable: this.extensionAvailable,
      cloudProxies: this.cloudProxies.map(proxy => ({
        name: proxy.name,
        available: proxy.available,
        priority: proxy.priority,
        rateLimit: proxy.rateLimit,
        currentRequests: proxy.currentRequests,
        usage: this.stats.proxyUsage[proxy.name] || { success: 0, failed: 0 }
      })),
      stats: this.stats,
      recommendations: this.getRecommendations()
    }
  }

  /**
   * 获取优化建议
   */
  getRecommendations() {
    const recommendations = []
    
    if (!this.localProxyManager.isRunning) {
      recommendations.push('建议启动本地代理服务器以获得最佳性能')
    }
    
    if (!this.extensionAvailable) {
      recommendations.push('安装浏览器扩展可以提供更稳定的跨域支持')
    }
    
    const failureRate = this.stats.failedRequests / this.stats.totalRequests
    if (failureRate > 0.3) {
      recommendations.push('请求失败率较高，建议检查网络连接或更换代理')
    }
    
    return recommendations
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      proxyUsage: {}
    }
  }
}

// 创建全局实例
const enhancedProxyManager = new EnhancedProxyManager()

export default enhancedProxyManager
