/**
 * 降级模式解析器
 * 当代理服务器不可用时，提供基础的书籍信息解析功能
 */

class FallbackParser {
  constructor() {
    this.isEnabled = false
  }

  /**
   * 启用降级模式
   */
  enable() {
    this.isEnabled = true
    console.log('📱 降级模式已启用')
  }

  /**
   * 禁用降级模式
   */
  disable() {
    this.isEnabled = false
    console.log('💻 降级模式已禁用')
  }

  /**
   * 从书籍ID生成基础信息
   */
  generateBasicBookInfo(bookId) {
    if (!bookId) {
      return null
    }

    return {
      novelId: bookId,
      novelName: `小说_${bookId}`,
      author: '未知作者',
      description: '暂无简介（降级模式）',
      coverUrl: '',
      totalChapters: 0,
      parseTime: new Date().toISOString(),
      source: 'fallback'
    }
  }

  /**
   * 尝试从URL中提取更多信息
   */
  extractInfoFromUrl(url) {
    const info = {
      source: 'url_analysis',
      hints: []
    }

    try {
      // 分析URL中可能包含的信息
      if (url.includes('changdunovel.com')) {
        info.platform = '番茄小说'
        info.hints.push('检测到番茄小说分享链接')
      } else if (url.includes('fanqienovel.com')) {
        info.platform = '番茄小说'
        info.hints.push('检测到番茄小说官方链接')
      }

      // 尝试从URL参数中提取更多信息
      const urlParams = new URLSearchParams(url.split('?')[1] || '')
      
      if (urlParams.has('share_type')) {
        info.shareType = urlParams.get('share_type')
        info.hints.push('检测到分享类型信息')
      }

      if (urlParams.has('entrance')) {
        info.entrance = urlParams.get('entrance')
        info.hints.push(`入口来源: ${info.entrance}`)
      }

      return info
    } catch (error) {
      console.warn('URL信息提取失败:', error)
      return info
    }
  }

  /**
   * 降级模式下的书籍信息解析
   */
  async parseBookInfo(bookId, originalUrl = '') {
    try {
      if (!this.isEnabled) {
        throw new Error('降级模式未启用')
      }

      console.log('📱 使用降级模式解析书籍信息...')

      // 生成基础信息
      const basicInfo = this.generateBasicBookInfo(bookId)
      if (!basicInfo) {
        throw new Error('无效的书籍ID')
      }

      // 尝试从URL提取额外信息
      const urlInfo = this.extractInfoFromUrl(originalUrl)

      // 合并信息
      const bookInfo = {
        ...basicInfo,
        platform: urlInfo.platform || '番茄小说',
        originalUrl: originalUrl,
        parseMethod: 'fallback',
        limitations: [
          '无法获取真实书名',
          '无法获取作者信息',
          '无法获取书籍简介',
          '无法获取章节数量'
        ],
        hints: urlInfo.hints || []
      }

      return {
        success: true,
        data: bookInfo,
        message: '降级模式解析完成'
      }

    } catch (error) {
      console.error('降级模式解析失败:', error)
      return {
        success: false,
        message: error.message || '降级模式解析失败',
        data: null
      }
    }
  }

  /**
   * 提供用户手动输入书籍信息的接口
   */
  createManualInputInterface(bookId) {
    return {
      bookId: bookId,
      fields: [
        {
          key: 'novelName',
          label: '书名',
          type: 'text',
          placeholder: '请输入书名',
          required: true
        },
        {
          key: 'author',
          label: '作者',
          type: 'text',
          placeholder: '请输入作者名',
          required: false
        },
        {
          key: 'description',
          label: '简介',
          type: 'textarea',
          placeholder: '请输入书籍简介（可选）',
          required: false
        }
      ],
      onSubmit: (data) => {
        return {
          novelId: bookId,
          novelName: data.novelName || `小说_${bookId}`,
          author: data.author || '未知作者',
          description: data.description || '暂无简介',
          coverUrl: '',
          totalChapters: 0,
          parseTime: new Date().toISOString(),
          source: 'manual_input'
        }
      }
    }
  }

  /**
   * 检查是否可以使用某些在线API作为备选
   */
  async checkAlternativeAPIs() {
    const alternatives = [
      {
        name: 'CORS代理',
        url: 'https://cors-anywhere.herokuapp.com/',
        test: async () => {
          try {
            const response = await fetch('https://cors-anywhere.herokuapp.com/https://httpbin.org/get')
            return response.ok
          } catch {
            return false
          }
        }
      },
      {
        name: 'AllOrigins代理',
        url: 'https://api.allorigins.win/',
        test: async () => {
          try {
            const response = await fetch('https://api.allorigins.win/get?url=https://httpbin.org/get')
            return response.ok
          } catch {
            return false
          }
        }
      }
    ]

    const available = []
    for (const alt of alternatives) {
      try {
        const isWorking = await alt.test()
        if (isWorking) {
          available.push(alt)
        }
      } catch (error) {
        console.warn(`测试 ${alt.name} 失败:`, error)
      }
    }

    return available
  }

  /**
   * 获取降级模式状态
   */
  getStatus() {
    return {
      isEnabled: this.isEnabled,
      capabilities: {
        basicParsing: true,
        urlAnalysis: true,
        manualInput: true,
        alternativeAPIs: false
      },
      limitations: [
        '无法获取真实书籍信息',
        '需要用户手动输入或确认',
        '功能受限但基本可用'
      ]
    }
  }
}

// 创建全局实例
const fallbackParser = new FallbackParser()

export default fallbackParser
