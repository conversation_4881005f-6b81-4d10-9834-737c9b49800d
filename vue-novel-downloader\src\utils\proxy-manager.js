/**
 * 智能代理服务器管理器
 * 支持多种环境：桌面浏览器、手机浏览器、本地代理、云端代理
 */

class ProxyManager {
  constructor() {
    this.proxyUrls = [
      'http://localhost:3001',           // 本地代理服务器
      'http://127.0.0.1:3001'           // 备用本地地址
    ]
    this.currentProxyUrl = null
    this.isRunning = false
    this.checkInterval = null
    this.startAttempts = 0
    this.maxStartAttempts = 3
    this.isMobile = this.detectMobile()
    this.fallbackMode = false
  }

  /**
   * 检测是否为移动设备
   */
  detectMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  }

  /**
   * 智能检查代理服务器状态
   * 会尝试多个代理地址，找到可用的
   */
  async checkProxyStatus() {
    // 如果已经有可用的代理，先检查它
    if (this.currentProxyUrl) {
      const isCurrentWorking = await this.testProxyUrl(this.currentProxyUrl)
      if (isCurrentWorking) {
        this.isRunning = true
        return true
      } else {
        this.currentProxyUrl = null
      }
    }

    // 尝试所有可能的代理地址
    for (const proxyUrl of this.proxyUrls) {
      const isWorking = await this.testProxyUrl(proxyUrl)
      if (isWorking) {
        this.currentProxyUrl = proxyUrl
        this.isRunning = true
        console.log(`✅ 找到可用代理服务器: ${proxyUrl}`)
        return true
      }
    }

    this.isRunning = false
    this.currentProxyUrl = null
    return false
  }

  /**
   * 测试单个代理URL是否可用
   */
  async testProxyUrl(proxyUrl) {
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 3000)

      const response = await fetch(`${proxyUrl}/health`, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Accept': 'application/json'
        }
      })

      clearTimeout(timeoutId)

      if (response.ok) {
        const data = await response.json()
        return data.status === 'ok'
      }

      return false
    } catch (error) {
      return false
    }
  }

  /**
   * 智能启动代理服务器
   * 根据环境自动选择最佳策略
   */
  async startProxyServer() {
    try {
      console.log('🔍 智能检查代理服务器状态...')

      // 首先检查是否已有可用代理
      const isAlreadyRunning = await this.checkProxyStatus()
      if (isAlreadyRunning) {
        console.log('✅ 代理服务器已经在运行')
        return true
      }

      // 根据环境选择启动策略
      if (this.isMobile) {
        return await this.handleMobileEnvironment()
      } else {
        return await this.handleDesktopEnvironment()
      }
    } catch (error) {
      console.error('❌ 启动代理服务器时出错:', error)
      return await this.enableFallbackMode()
    }
  }

  /**
   * 处理移动设备环境
   */
  async handleMobileEnvironment() {
    console.log('📱 检测到移动设备环境')

    // 移动设备无法启动本地代理，尝试使用云端代理或降级模式
    const cloudProxyAvailable = await this.checkCloudProxy()
    if (cloudProxyAvailable) {
      console.log('☁️ 使用云端代理服务器')
      return true
    }

    // 启用降级模式
    return await this.enableFallbackMode()
  }

  /**
   * 处理桌面环境
   */
  async handleDesktopEnvironment() {
    console.log('💻 检测到桌面环境')

    // 尝试自动启动本地代理服务器
    const autoStarted = await this.tryAutoStartProxy()
    if (autoStarted) {
      return true
    }

    // 尝试后台启动
    const backgroundStarted = await this.tryBackgroundStart()
    if (backgroundStarted) {
      return true
    }

    // 自动启动失败，提供用户指导
    this.showProxyInstructions()

    // 开始监听用户手动启动
    this.startStatusCheck()
    return false
  }

  /**
   * 尝试后台启动代理服务器
   */
  async tryBackgroundStart() {
    try {
      console.log('🔄 尝试后台启动代理服务器...')

      // 方法1: 尝试通过隐藏的iframe启动后台脚本
      const iframeResult = await this.startViaHiddenIframe()
      if (iframeResult) {
        console.log('✅ 通过iframe后台启动成功')
        return true
      }

      // 方法2: 尝试通过Web Worker启动
      const workerResult = await this.startViaWebWorker()
      if (workerResult) {
        console.log('✅ 通过Web Worker后台启动成功')
        return true
      }

      // 方法3: 尝试通过动态脚本启动
      const scriptResult = await this.startViaDynamicScript()
      if (scriptResult) {
        console.log('✅ 通过动态脚本后台启动成功')
        return true
      }

      return false
    } catch (error) {
      console.warn('后台启动失败:', error.message)
      return false
    }
  }

  /**
   * 通过安全方式检测代理服务器
   */
  async startViaHiddenIframe() {
    try {
      console.log('🔍 检测代理服务器状态...')

      // 直接检查代理服务器是否已运行，不尝试启动
      const isRunning = await this.checkProxyStatus()

      if (isRunning) {
        console.log('✅ 代理服务器已在运行')
        return true
      } else {
        console.log('⚠️ 代理服务器未运行，需要手动启动')
        return false
      }

    } catch (error) {
      console.warn('代理服务器检测失败:', error)
      return false
    }
  }

  /**
   * 通过Web Worker启动
   */
  async startViaWebWorker() {
    try {
      if (!window.Worker) {
        return false
      }

      // 创建内联Web Worker
      const workerScript = `
        self.onmessage = async function(e) {
          if (e.data.action === 'startProxy') {
            try {
              // 尝试启动代理服务器
              const response = await fetch('http://localhost:3001/auto-start', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
              });

              self.postMessage({ success: response.ok });
            } catch (error) {
              self.postMessage({ success: false, error: error.message });
            }
          }
        };
      `

      const blob = new Blob([workerScript], { type: 'application/javascript' })
      const worker = new Worker(URL.createObjectURL(blob))

      return new Promise((resolve) => {
        worker.onmessage = (e) => {
          const { success } = e.data
          worker.terminate()
          URL.revokeObjectURL(blob)
          resolve(success)
        }

        worker.onerror = () => {
          worker.terminate()
          URL.revokeObjectURL(blob)
          resolve(false)
        }

        worker.postMessage({ action: 'startProxy' })

        // 超时处理
        setTimeout(() => {
          worker.terminate()
          URL.revokeObjectURL(blob)
          resolve(false)
        }, 5000)
      })

    } catch (error) {
      console.warn('Web Worker启动方法失败:', error)
      return false
    }
  }

  /**
   * 通过动态脚本启动
   */
  async startViaDynamicScript() {
    try {
      // 创建多个脚本标签尝试触发启动
      const scripts = [
        'http://localhost:3001/health',
        'http://localhost:3001/auto-start',
        'http://localhost:3001/status'
      ]

      for (const src of scripts) {
        try {
          const script = document.createElement('script')
          script.src = src + '?callback=proxyStartCallback&t=' + Date.now()
          script.async = true
          script.onerror = () => document.head.removeChild(script)
          script.onload = () => document.head.removeChild(script)

          document.head.appendChild(script)

          // 等待一段时间
          await new Promise(resolve => setTimeout(resolve, 1000))
        } catch (error) {
          continue
        }
      }

      // 等待启动完成
      await new Promise(resolve => setTimeout(resolve, 2000))

      // 检查是否启动成功
      return await this.checkProxyStatus()

    } catch (error) {
      console.warn('动态脚本启动方法失败:', error)
      return false
    }
  }

  /**
   * 尝试自动启动代理服务器
   */
  async tryAutoStartProxy() {
    try {
      console.log('🚀 尝试自动启动代理服务器...')

      // 方法1: 尝试通过自动启动接口
      const autoStartResult = await this.tryAutoStartViaAPI()
      if (autoStartResult) {
        return true
      }

      // 方法2: 在支持的环境中尝试自动启动
      if (window.electronAPI) {
        // Electron环境
        return await this.startProxyInElectron()
      } else if (window.chrome && window.chrome.runtime) {
        // Chrome扩展环境
        return await this.startProxyInExtension()
      } else if (this.canUseServiceWorker()) {
        // Service Worker环境
        return await this.startProxyViaServiceWorker()
      } else {
        // 普通浏览器环境，尝试其他方法
        return await this.tryBrowserAutoStart()
      }
    } catch (error) {
      console.log('自动启动失败，需要手动启动:', error.message)
      return false
    }
  }

  /**
   * 通过API接口尝试自动启动
   */
  async tryAutoStartViaAPI() {
    try {
      // 尝试调用自动启动接口
      const response = await fetch('http://localhost:3001/auto-start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 5000
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          console.log('✅ 通过API自动启动成功')
          return true
        }
      }

      return false
    } catch (error) {
      // API调用失败，尝试其他方法
      return false
    }
  }

  /**
   * 检查是否可以使用Service Worker
   */
  canUseServiceWorker() {
    return 'serviceWorker' in navigator && 'MessageChannel' in window
  }

  /**
   * 通过Service Worker启动代理
   */
  async startProxyViaServiceWorker() {
    try {
      if (!this.canUseServiceWorker()) {
        return false
      }

      // 注册Service Worker（如果还没有）
      const registration = await navigator.serviceWorker.register('/proxy-worker.js')

      // 等待Service Worker激活
      await navigator.serviceWorker.ready

      // 发送启动代理的消息
      const messageChannel = new MessageChannel()

      return new Promise((resolve) => {
        messageChannel.port1.onmessage = (event) => {
          const { success } = event.data
          resolve(success)
        }

        navigator.serviceWorker.controller?.postMessage({
          action: 'startProxy',
          port: 3001
        }, [messageChannel.port2])

        // 超时处理
        setTimeout(() => resolve(false), 5000)
      })

    } catch (error) {
      console.warn('Service Worker启动失败:', error)
      return false
    }
  }

  /**
   * 浏览器环境代理检测
   */
  async tryBrowserAutoStart() {
    try {
      console.log('🔍 浏览器环境代理检测...')

      // 直接检查代理服务器状态，不尝试自动启动
      const isRunning = await this.checkProxyStatus()

      if (isRunning) {
        console.log('✅ 代理服务器检测成功')
        return true
      } else {
        console.log('⚠️ 代理服务器未检测到')
        // 显示用户友好的提示
        this.showProxyInstructions()
        return false
      }

    } catch (error) {
      console.warn('浏览器代理检测失败:', error)
      return false
    }
  }

  /**
   * 检查云端代理是否可用
   */
  async checkCloudProxy() {
    const cloudUrls = this.proxyUrls.filter(url => url.startsWith('https://'))
    for (const url of cloudUrls) {
      const isWorking = await this.testProxyUrl(url)
      if (isWorking) {
        this.currentProxyUrl = url
        return true
      }
    }
    return false
  }

  /**
   * 启用降级模式
   */
  async enableFallbackMode() {
    console.log('🔄 启用降级模式')
    this.fallbackMode = true

    // 触发降级模式事件
    window.dispatchEvent(new CustomEvent('proxy-fallback-mode', {
      detail: {
        message: '代理服务器不可用，已启用降级模式',
        limitations: [
          '无法自动解析书籍信息',
          '需要手动输入书籍ID',
          '部分功能可能受限'
        ]
      }
    }))

    return false
  }

  /**
   * 显示代理服务器启动指导
   */
  showProxyInstructions() {
    const instructions = this.isMobile ? [
      '移动设备无法启动本地代理服务器',
      '建议在电脑上使用完整功能',
      '或等待云端代理服务器部署'
    ] : [
      '双击项目根目录下的 start-proxy.bat 文件',
      '或在项目根目录运行命令: node proxy-server.js',
      '启动后系统会自动检测并提示'
    ]

    window.dispatchEvent(new CustomEvent('proxy-server-needed', {
      detail: {
        message: '需要启动代理服务器以使用书籍解析功能',
        instructions: instructions,
        isMobile: this.isMobile
      }
    }))
  }

  /**
   * 开始定期检查代理服务器状态
   */
  startStatusCheck() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
    }

    this.checkInterval = setInterval(async () => {
      const isRunning = await this.checkProxyStatus()

      if (isRunning && !this.isRunning) {
        console.log('✅ 代理服务器已启动并正常运行')
        this.stopStatusCheck()

        // 触发自定义事件通知应用
        window.dispatchEvent(new CustomEvent('proxy-server-ready'))
      }
    }, 2000) // 每2秒检查一次
  }

  /**
   * 停止状态检查
   */
  stopStatusCheck() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }
  }

  /**
   * 等待代理服务器就绪
   */
  async waitForProxy(timeout = 30000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now()

      const checkStatus = async () => {
        const isRunning = await this.checkProxyStatus()

        if (isRunning) {
          resolve(true)
          return
        }

        if (Date.now() - startTime > timeout) {
          reject(new Error('等待代理服务器超时'))
          return
        }

        setTimeout(checkStatus, 1000)
      }

      checkStatus()
    })
  }

  /**
   * 获取代理服务器状态信息
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      currentProxyUrl: this.currentProxyUrl,
      isMobile: this.isMobile,
      fallbackMode: this.fallbackMode,
      startAttempts: this.startAttempts,
      maxStartAttempts: this.maxStartAttempts,
      availableProxies: this.proxyUrls
    }
  }

  /**
   * 获取当前代理URL
   */
  getCurrentProxyUrl() {
    return this.currentProxyUrl || this.proxyUrls[0]
  }

  /**
   * 是否为降级模式
   */
  isFallbackMode() {
    return this.fallbackMode
  }

  /**
   * 手动设置代理URL
   */
  setProxyUrl(url) {
    if (url && typeof url === 'string') {
      this.currentProxyUrl = url
      if (!this.proxyUrls.includes(url)) {
        this.proxyUrls.unshift(url)
      }
    }
  }

  /**
   * 在Electron环境中启动代理
   */
  async startProxyInElectron() {
    try {
      if (window.electronAPI && window.electronAPI.startProxy) {
        const result = await window.electronAPI.startProxy()
        if (result.success) {
          console.log('✅ Electron环境代理启动成功')
          return true
        }
      }
      return false
    } catch (error) {
      console.error('Electron代理启动失败:', error)
      return false
    }
  }

  /**
   * 在Chrome扩展环境中启动代理
   */
  async startProxyInExtension() {
    try {
      if (window.chrome && window.chrome.runtime) {
        // Chrome扩展可以使用background script启动代理
        return new Promise((resolve) => {
          chrome.runtime.sendMessage({
            action: 'startProxy'
          }, (response) => {
            resolve(response && response.success)
          })
        })
      }
      return false
    } catch (error) {
      console.error('Chrome扩展代理启动失败:', error)
      return false
    }
  }

  /**
   * 重置启动尝试计数
   */
  resetStartAttempts() {
    this.startAttempts = 0
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.stopStatusCheck()
    this.isRunning = false
    this.startAttempts = 0
  }
}

// 创建全局实例
const proxyManager = new ProxyManager()

export default proxyManager
