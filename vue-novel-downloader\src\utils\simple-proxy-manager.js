/**
 * 简化的代理管理器
 * 专注于稳定性和用户体验，移除不安全的自动启动功能
 */

class SimpleProxyManager {
  constructor() {
    this.proxyUrls = [
      'http://localhost:3001',
      'http://127.0.0.1:3001'
    ]
    this.currentProxyUrl = null
    this.isRunning = false
    this.checkInterval = null
    this.isMobile = this.detectMobile()
    this.fallbackMode = false
    this.lastCheckTime = 0
    this.checkCooldown = 5000 // 5秒检查冷却时间
  }

  /**
   * 检测是否为移动设备
   */
  detectMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  }

  /**
   * 智能检查代理服务器状态（带冷却时间）
   */
  async checkProxyStatus() {
    const now = Date.now()
    
    // 如果在冷却时间内，返回上次的结果
    if (now - this.lastCheckTime < this.checkCooldown && this.lastCheckTime > 0) {
      return this.isRunning
    }

    this.lastCheckTime = now

    // 如果已经有可用的代理，先检查它
    if (this.currentProxyUrl) {
      const isCurrentWorking = await this.testProxyUrl(this.currentProxyUrl)
      if (isCurrentWorking) {
        this.isRunning = true
        return true
      } else {
        this.currentProxyUrl = null
      }
    }

    // 尝试所有可能的代理地址
    for (const proxyUrl of this.proxyUrls) {
      const isWorking = await this.testProxyUrl(proxyUrl)
      if (isWorking) {
        this.currentProxyUrl = proxyUrl
        this.isRunning = true
        console.log(`✅ 找到可用代理服务器: ${proxyUrl}`)
        return true
      }
    }

    this.isRunning = false
    this.currentProxyUrl = null
    return false
  }

  /**
   * 测试单个代理URL是否可用
   */
  async testProxyUrl(proxyUrl) {
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 3000)

      const response = await fetch(`${proxyUrl}/health`, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Accept': 'application/json'
        }
      })

      clearTimeout(timeoutId)

      if (response.ok) {
        const data = await response.json()
        return data.status === 'ok'
      }

      return false
    } catch (error) {
      return false
    }
  }

  /**
   * 获取页面内容
   */
  async fetchPageContent(url) {
    try {
      // 检查代理状态
      const proxyAvailable = await this.checkProxyStatus()
      
      if (!proxyAvailable) {
        throw new Error('代理服务器不可用')
      }

      const proxyUrl = `${this.currentProxyUrl}/proxy/fetch-page`

      console.log(`📡 使用代理服务器: ${this.currentProxyUrl}`)

      const response = await fetch(proxyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          url,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          }
        })
      })

      if (!response.ok) {
        throw new Error(`代理请求失败: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || '代理请求失败')
      }

      return {
        success: true,
        html: result.html,
        status: result.status,
        headers: result.headers
      }

    } catch (error) {
      console.error('代理请求失败:', error)
      throw error
    }
  }

  /**
   * 启用降级模式
   */
  enableFallbackMode() {
    console.log('🔄 启用降级模式')
    this.fallbackMode = true

    // 触发降级模式事件
    window.dispatchEvent(new CustomEvent('proxy-fallback-mode', {
      detail: {
        message: '代理服务器不可用，已启用降级模式',
        limitations: [
          '无法自动解析书籍信息',
          '需要手动输入书籍ID',
          '部分功能可能受限'
        ]
      }
    }))

    return false
  }

  /**
   * 显示用户友好的代理启动指导
   */
  showProxyInstructions() {
    const instructions = {
      desktop: [
        '1. 双击运行 "start-proxy-enhanced.bat" 文件',
        '2. 或在项目根目录运行命令: node proxy-server.js',
        '3. 看到 "代理服务器启动成功" 提示后，刷新此页面',
        '4. 保持代理服务器窗口打开'
      ],
      mobile: [
        '移动设备无法启动本地代理服务器',
        '建议在电脑上使用完整功能',
        '或等待云端代理服务器部署'
      ]
    }

    const currentInstructions = this.isMobile ? instructions.mobile : instructions.desktop

    window.dispatchEvent(new CustomEvent('proxy-server-needed', {
      detail: {
        title: '需要启动代理服务器',
        message: '书籍解析功能需要代理服务器支持',
        instructions: currentInstructions,
        isMobile: this.isMobile,
        actions: this.isMobile ? [
          { text: '启用降级模式', action: 'enableFallback' }
        ] : [
          { text: '重新检测', action: 'recheck' },
          { text: '查看详细说明', action: 'showDetails' }
        ]
      }
    }))
  }

  /**
   * 开始定期检查代理服务器状态
   */
  startStatusCheck() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
    }

    this.checkInterval = setInterval(async () => {
      const wasRunning = this.isRunning
      const isRunning = await this.checkProxyStatus()

      // 状态发生变化时触发事件
      if (isRunning && !wasRunning) {
        console.log('✅ 代理服务器已启动并正常运行')
        this.stopStatusCheck()
        window.dispatchEvent(new CustomEvent('proxy-server-ready', {
          detail: { proxyUrl: this.currentProxyUrl }
        }))
      } else if (!isRunning && wasRunning) {
        console.log('⚠️ 代理服务器连接丢失')
        window.dispatchEvent(new CustomEvent('proxy-server-lost'))
      }
    }, 3000) // 每3秒检查一次
  }

  /**
   * 停止状态检查
   */
  stopStatusCheck() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }
  }

  /**
   * 获取代理服务器状态信息
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      currentProxyUrl: this.currentProxyUrl,
      isMobile: this.isMobile,
      fallbackMode: this.fallbackMode,
      availableProxies: this.proxyUrls,
      lastCheckTime: this.lastCheckTime,
      recommendations: this.getRecommendations()
    }
  }

  /**
   * 获取使用建议
   */
  getRecommendations() {
    const recommendations = []

    if (!this.isRunning) {
      if (this.isMobile) {
        recommendations.push('移动设备建议使用降级模式或在电脑上使用')
      } else {
        recommendations.push('请启动本地代理服务器以获得完整功能')
      }
    }

    if (this.fallbackMode) {
      recommendations.push('当前为降级模式，功能受限但基本可用')
    }

    return recommendations
  }

  /**
   * 获取当前代理URL
   */
  getCurrentProxyUrl() {
    return this.currentProxyUrl || this.proxyUrls[0]
  }

  /**
   * 是否为降级模式
   */
  isFallbackMode() {
    return this.fallbackMode
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.stopStatusCheck()
    this.isRunning = false
  }
}

// 创建全局实例
const simpleProxyManager = new SimpleProxyManager()

export default simpleProxyManager
