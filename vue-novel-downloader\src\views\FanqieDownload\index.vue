<template>
  <div class="fanqie-download">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="logo-section">
          <div class="logo">
            <i class="el-icon-reading"></i>
            <h1>Useful小说下载器</h1>
          </div>
          <p class="subtitle">简单、快速、高效的小说下载工具</p>
        </div>

        <div class="user-section">
          <div class="user-info" v-if="isActivated">
            <el-tag type="success" size="small">
              <i class="el-icon-check"></i>
              已激活
            </el-tag>
            <span class="remaining">今日剩余下载: {{ remainingDownloads }}</span>
            <el-button
              type="text"
              size="mini"
              @click="refreshDownloadCount"
              :loading="isRefreshingCount"
              class="refresh-btn"
            >
              <i class="el-icon-refresh"></i>
              刷新
            </el-button>
          </div>
          <el-button v-else type="primary" size="small" @click="goToActivation">
            <i class="el-icon-unlock"></i>
            激活软件
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 下载表单 -->
      <el-card class="download-card" shadow="hover">
        <div slot="header" class="card-header">
          <i class="el-icon-download"></i>
          <span>下载小说</span>
        </div>

        <el-form :model="form" :rules="rules" ref="downloadForm" label-width="0">
          <div class="form-row">
            <el-form-item prop="novelUrl" class="url-input">
              <el-input
                v-model="form.novelUrl"
                placeholder="请输入番茄小说链接或书籍ID，支持分享链接，例如：https://changdunovel.com/wap/share-v2.html?book_id=123456 或 123456"
                size="large"
                clearable
                @keyup.enter="startDownload"
              >
                <template slot="prepend">
                  <i class="el-icon-link"></i>
                </template>
              </el-input>
            </el-form-item>
          </div>


        </el-form>

        <!-- 解析和下载按钮 -->
        <!-- 智能代理状态提示 -->
        <div v-if="!proxyServerStatus.isRunning" class="proxy-status-alert">
          <el-alert
            :title="proxyServerStatus.isFallbackMode ? '降级模式已启用' : '代理服务器未运行'"
            :type="proxyServerStatus.isFallbackMode ? 'info' : 'warning'"
            :closable="false"
            show-icon
          >
            <template slot="default">
              <div v-if="proxyServerStatus.isFallbackMode">
                <p>📱 当前运行在降级模式下，功能可能受限但基本可用。</p>
                <ul style="margin: 10px 0; padding-left: 20px; font-size: 13px;">
                  <li>✅ 支持书籍ID提取和基础下载</li>
                  <li>⚠️ 无法自动获取书名、作者等信息</li>
                  <li>💡 建议在电脑上启动代理服务器获得完整功能</li>
                </ul>
              </div>
              <div v-else>
                <p>书籍解析功能需要代理服务器支持。{{ proxyServerStatus.isMobile ? '移动设备' : '桌面设备' }}检测到。</p>
                <p v-if="proxyServerStatus.isMobile" style="color: #e6a23c; font-size: 13px;">
                  💡 移动设备将自动启用降级模式，功能可能受限
                </p>
              </div>
              <div class="proxy-actions">
                <el-button
                  type="text"
                  size="small"
                  @click="checkProxyStatus"
                  :loading="proxyServerStatus.isChecking"
                >
                  <i class="el-icon-refresh"></i>
                  重新检测
                </el-button>
                <el-button
                  v-if="!proxyServerStatus.isMobile"
                  type="text"
                  size="small"
                  @click="showProxyInstructions"
                >
                  <i class="el-icon-question"></i>
                  启动说明
                </el-button>
                <el-button
                  v-if="proxyServerStatus.isFallbackMode"
                  type="text"
                  size="small"
                  @click="enableFallbackMode"
                >
                  <i class="el-icon-mobile-phone"></i>
                  降级模式说明
                </el-button>
              </div>
            </template>
          </el-alert>
        </div>

        <div class="download-actions">

          <el-button
            type="primary"
            size="large"
            icon="el-icon-download"
            @click="startDownload"
            :loading="isDownloading || isParsing"
            :disabled="!canStartDownload"
            class="download-btn"
          >
            {{ isDownloading ? '下载中...' : isParsing ? '解析中...' : '开始下载' }}
          </el-button>

          <el-button
            v-if="isDownloading"
            type="danger"
            size="large"
            icon="el-icon-close"
            @click="stopDownload"
            class="stop-btn"
          >
            停止下载
          </el-button>
        </div>
      </el-card>
      <!-- 书籍信息显示区域 -->
      <el-card v-if="novelInfo.novelName && novelInfo.novelName !== `小说_${novelInfo.novelId}`" class="book-info-card" shadow="hover">
        <div slot="header" class="card-header">
          <i class="el-icon-reading"></i>
          <span>书籍信息</span>
        </div>

        <div class="book-info-content">
          <div class="book-basic-info">
            <h3 class="book-title">《{{ novelInfo.novelName }}》</h3>
            <p class="book-author">作者：{{ novelInfo.novelAuthor }}</p>
            <p class="book-desc">{{ novelInfo.novelDesc }}</p>
          </div>

          <div class="book-meta">
            <el-tag size="small" type="info">ID: {{ novelInfo.novelId }}</el-tag>
            <el-tag size="small" type="success" v-if="novelInfo.totalChapters > 0">
              章节数: {{ novelInfo.totalChapters }}
            </el-tag>
          </div>
        </div>
      </el-card>

        <!-- 下载进度 -->
        <div v-if="downloadStatus.progress > 0" class="download-progress">
          <div class="progress-header">
            <span class="progress-title">下载进度</span>
            <span class="progress-percent">{{ downloadStatus.progress.toFixed(1) }}%</span>
          </div>
          <el-progress
            :percentage="downloadStatus.progress"
            :status="downloadStatus.progress === 100 ? 'success' : null"
            :stroke-width="12"
            :show-text="false"
						style="margin: 10px 0;"
          />
          <!--<div class="progress-details">-->
          <!--  <span>{{ downloadStatus.currentChapter }}</span>-->
          <!--  <span v-if="downloadStatus.speed > 0">{{ downloadStatus.speed.toFixed(1) }} 章/分钟</span>-->
          <!--  <span v-if="downloadStatus.eta > 0">剩余 {{ formatTime(downloadStatus.eta) }}</span>-->
          <!--</div>-->

          <!-- 下载完成后的文件下载按钮 -->
          <div v-if="downloadStatus.downloadCompleted && downloadStatus.fileUrl" class="download-complete">
            <el-alert
              title="下载完成！"
              type="success"
              :closable="false"
              show-icon
              class="complete-alert"
            >
              <template slot="default">
                <p>小说《{{ novelInfo.novelName }}》已成功下载完成！</p>
                <div class="download-actions">
                  <el-button
                    type="primary"
                    icon="el-icon-download"
                    @click="downloadCompletedFile"
                    size="small"
                  >
                    下载文件
                  </el-button>
                  <el-button
                    type="info"
                    icon="el-icon-refresh"
                    @click="refreshPage"
                    size="small"
                    plain
                  >
                    刷新页面
                  </el-button>
                </div>
              </template>
            </el-alert>
          </div>

          <!-- 下载失败提示 -->
          <div v-if="downloadStatus.status === 'failed'" class="download-failed">
            <el-alert
              title="下载失败"
              type="error"
              :closable="false"
              show-icon
              class="failed-alert"
            >
              <template slot="default">
                <p>{{ downloadStatus.errorMessage || '下载过程中发生未知错误' }}</p>
                <el-button
                  type="danger"
                  icon="el-icon-refresh"
                  @click="retryDownload"
                  size="small"
                  plain
                >
                  重试下载
                </el-button>
              </template>
            </el-alert>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import { ErrorHandler } from '@/utils/error-handler'
import proxyManager from '@/utils/proxy-manager'

export default {
  name: 'FanqieDownload',

  data() {
    return {
      $errorHandler: ErrorHandler,

      form: {
        novelUrl: ''
        // 固定使用txt格式，去除其他选项
      },

      // 解析状态
      isParsing: false,

      // 刷新下载次数状态
      isRefreshingCount: false,

      // 页面可见性监听器
      visibilityChangeHandler: null,

      // 代理服务器状态
      proxyServerStatus: {
        isRunning: false,
        isChecking: false,
        isFallbackMode: false,
        isMobile: false
      },

      // 添加novelInfo以避免模板错误
      novelInfo: {
        novelId: '',
        novelName: '',
        novelAuthor: '',
        novelDesc: '',
        coverUrl: '',
        type: 'web',
        totalChapters: 0
      },

      rules: {
        novelUrl: [
          { required: true, message: '请输入小说链接或书籍ID', trigger: 'blur' },
          {
            validator: (_, value, callback) => {
              if (!value) {
                callback(new Error('请输入小说链接或书籍ID'))
                return
              }
              // 检查是否为纯数字ID或包含番茄小说相关域名的链接，或包含book_id参数
              if (/^\d+$/.test(value.trim()) ||
                  /fanqienovel\.com/.test(value) ||
                  /changdunovel\.com/.test(value) ||
                  /book_id=\d+/.test(value)) {
                callback()
              } else {
                callback(new Error('请输入有效的番茄小说链接或纯数字书籍ID'))
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },

  computed: {
    ...mapGetters({
      downloadStatus: 'download/downloadStatus',
      isDownloading: 'download/isDownloading',
      isActivated: 'user/isActivated',
      remainingDownloads: 'user/remainingDownloads'
    }),

    // 是否可以开始下载
    canStartDownload() {
      return this.form.novelUrl.trim() &&
             !this.isDownloading &&
             !this.isParsing &&
             this.$store.getters['user/isActivated']
    }
  },

  filters: {
    formatNumber(value) {
      if (!value) return '0'
      return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    }
  },

  async mounted() {
    // 检查代理服务器状态
    await this.checkProxyStatus()

    // 组件挂载后自动获取剩余下载次数
    await this.autoRefreshDownloadCount()

    // 监听页面可见性变化，当用户切换回页面时自动刷新
    this.setupVisibilityListener()
  },

  beforeDestroy() {
    // 清理页面可见性监听器
    this.cleanupVisibilityListener()
  },

  methods: {
    ...mapActions({
      setCurrentDownload: 'download/setCurrentDownload',
      startDownloadAction: 'download/startDownload',
      stopDownloadAction: 'download/stopDownload',
      resetDownload: 'download/resetDownload',
      downloadFileAction: 'download/downloadFile',
      checkUserQuotaAction: 'download/checkUserQuota',
      getDownloadCountAction: 'user/getDownloadCount'
    }),

    // 跳转到激活页面
    goToActivation() {
      this.$router.push('/activation')
    },

    // 智能检查代理服务器状态
    async checkProxyStatus() {
      try {
        this.proxyServerStatus.isChecking = true

        // 使用智能代理管理器检查状态
        const isRunning = await proxyManager.checkProxyStatus()
        const status = proxyManager.getStatus()

        // 更新状态
        this.proxyServerStatus.isRunning = isRunning
        this.proxyServerStatus.isFallbackMode = status.fallbackMode
        this.proxyServerStatus.isMobile = status.isMobile

        if (isRunning) {
          console.log('✅ 代理服务器运行正常')
          this.$message.success(`代理服务器连接成功 (${status.currentProxyUrl})`)
        } else if (status.fallbackMode) {
          console.log('📱 降级模式已启用')
          this.$message.info('降级模式已启用，功能可能受限但基本可用')
        } else {
          console.log('⚠️ 代理服务器未运行')
          if (status.isMobile) {
            this.$message.warning('移动设备检测到，建议启用降级模式')
          }
        }

        return isRunning
      } catch (error) {
        console.error('检查代理服务器状态失败:', error)
        this.proxyServerStatus.isRunning = false
        this.proxyServerStatus.isFallbackMode = false
        return false
      } finally {
        this.proxyServerStatus.isChecking = false
      }
    },

    // 显示代理服务器启动说明
    showProxyInstructions() {
      const isMobile = proxyManager.getStatus().isMobile

      if (isMobile) {
        this.$alert(`
          <div style="text-align: left; line-height: 1.6;">
            <h4>📱 移动设备使用说明：</h4>
            <p><strong>当前环境：</strong>移动设备浏览器</p>
            <p><strong>限制：</strong>无法启动本地代理服务器</p>
            <br>
            <p><strong>建议方案：</strong></p>
            <ol style="margin: 10px 0; padding-left: 20px;">
              <li>使用降级模式（功能受限但可用）</li>
              <li>在电脑上使用完整功能</li>
              <li>等待云端代理服务器部署</li>
            </ol>
            <br>
            <p><strong>降级模式功能：</strong></p>
            <p>• ✅ 支持书籍ID提取和下载</p>
            <p>• ⚠️ 需要手动输入书名等信息</p>
          </div>
        `, '移动设备使用说明', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '启用降级模式',
          cancelButtonText: '我知道了',
          showCancelButton: true
        }).then(() => {
          this.enableFallbackMode()
        }).catch(() => {})
      } else {
        this.$alert(`
          <div style="text-align: left; line-height: 1.6;">
            <h4>🖥️ 桌面设备代理服务器启动方法：</h4>
            <p><strong>方法1（推荐）：</strong></p>
            <p>双击项目根目录下的 <code>start-proxy.bat</code> 文件</p>
            <br>
            <p><strong>方法2：</strong></p>
            <p>在项目根目录打开命令行，运行：</p>
            <p><code>node proxy-server.js</code></p>
            <br>
            <p><strong>注意：</strong></p>
            <p>• 代理服务器运行在端口 3001</p>
            <p>• 启动后点击"重新检测"按钮</p>
            <p>• 关闭浏览器不会影响代理服务器运行</p>
            <p>• 代理服务器用于解析书籍信息，不影响下载功能</p>
          </div>
        `, '代理服务器启动说明', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '我知道了'
        })
      }
    },

    // 启用降级模式
    enableFallbackMode() {
      this.$alert(`
        <div style="text-align: left; line-height: 1.6;">
          <h4>📱 降级模式说明</h4>
          <p><strong>当前状态：</strong>降级模式已启用</p>
          <br>
          <p><strong>可用功能：</strong></p>
          <ul style="margin: 10px 0; padding-left: 20px;">
            <li>✅ 书籍ID提取和验证</li>
            <li>✅ 基础下载功能</li>
            <li>✅ 手动输入书籍信息</li>
          </ul>
          <br>
          <p><strong>受限功能：</strong></p>
          <ul style="margin: 10px 0; padding-left: 20px;">
            <li>❌ 自动解析书名、作者</li>
            <li>❌ 自动获取书籍简介</li>
            <li>❌ 自动获取章节数量</li>
          </ul>
          <br>
          <p><strong>使用建议：</strong></p>
          <p>• 输入书籍ID后，系统会提示手动输入书名等信息</p>
          <p>• 下载功能不受影响，可正常使用</p>
          <p>• 如需完整功能，请在电脑上启动代理服务器</p>
        </div>
      `, '降级模式说明', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '我知道了'
      })
    },

    // 自动刷新剩余下载次数（静默获取，不显示提示）
    async autoRefreshDownloadCount() {
      try {
        // 只有在已激活状态下才自动获取
        if (!this.$store.getters['user/isActivated']) {
          return
        }

        const result = await this.getDownloadCountAction()

        if (result.success) {
          const downloadData = result.data || {}
          const downloadCount = downloadData.remainingDownloads || 0

          // 静默更新，不显示成功提示
          // 只在剩余次数很少时显示警告
          if (downloadCount <= 5 && downloadCount > 0) {
            this.$message.warning(`提醒：剩余下载次数不足，仅剩 ${downloadCount} 次`)
          } else if (downloadCount === 0) {
            this.$message.error('剩余下载次数已用完，请联系管理员')
          }
        } else {
          console.warn('自动获取剩余下载次数失败:', result.error)
          // 静默失败，不显示错误提示，避免干扰用户
        }
      } catch (error) {
        console.error('自动获取剩余下载次数异常:', error)
        // 静默处理异常，不显示错误提示
      }
    },

    // 设置页面可见性监听器
    setupVisibilityListener() {
      // 创建监听器函数
      this.visibilityChangeHandler = async () => {
        // 当页面从隐藏变为可见时，自动刷新剩余下载次数
        if (!document.hidden) {
          // 延迟500ms执行，避免频繁切换
          setTimeout(() => {
            this.autoRefreshDownloadCount()
          }, 500)
        }
      }

      // 添加监听器
      if (typeof document.addEventListener !== 'undefined') {
        document.addEventListener('visibilitychange', this.visibilityChangeHandler)
      }
    },

    // 清理页面可见性监听器
    cleanupVisibilityListener() {
      if (this.visibilityChangeHandler && typeof document.removeEventListener !== 'undefined') {
        document.removeEventListener('visibilitychange', this.visibilityChangeHandler)
        this.visibilityChangeHandler = null
      }
    },

    // 刷新剩余下载次数
    async refreshDownloadCount() {
      try {
        if (!this.$store.getters['user/isActivated']) {
          this.$message.error('请先激活软件')
          return
        }

        this.isRefreshingCount = true

        const result = await this.getDownloadCountAction()

        if (result.success) {
          const downloadData = result.data || {}
          const downloadCount = downloadData.remainingDownloads || 0
          this.$message.success(`刷新成功！剩余下载次数: ${downloadCount}`)

          // 显示详细信息
          // this.$notify({
          //   title: '下载次数信息',
          //   message: `
          //     <div style="line-height: 1.6;">
          //       <p><strong>剩余下载:</strong> ${downloadData.remainingDownloads}</p>
          //       <p><strong>总下载数:</strong> ${downloadData.totalDownloads}</p>
          //       <p><strong>已使用:</strong> ${downloadData.usedDownloads}</p>
          //       <p><strong>更新时间:</strong> ${new Date(downloadData.lastUpdated).toLocaleString()}</p>
          //     </div>
          //   `,
          //   dangerouslyUseHTMLString: true,
          //   type: 'success',
          //   duration: 5000
          // })
        } else {
          this.$message.error('刷新失败：' + result.error)
        }
      } catch (error) {
        console.error('刷新剩余下载次数失败:', error)
        this.$message.error('刷新失败：' + error.message)
      } finally {
        this.isRefreshingCount = false
      }
    },

    // 解析书籍信息
    async parseBookInfo() {
      try {
        if (!this.form.novelUrl.trim()) {
          this.$message.error('请输入小说链接或书籍ID')
          return
        }

        // 检查代理服务器状态
        const proxyRunning = await this.checkProxyStatus()
        if (!proxyRunning) {
          this.$message.error('代理服务器未运行，无法解析书籍信息。请先启动代理服务器。')
          this.showProxyInstructions()
          return
        }

        this.isParsing = true

        // 提取书籍ID
        const bookId = this.$api.novel.extractBookId(this.form.novelUrl)
        if (!bookId) {
          this.$message.error('无效的小说链接，请检查链接格式')
          return
        }

        this.$message.info('正在解析书籍信息，请稍候...')

        // 调用解析API
        const result = await this.$api.novel.parseBookInfo(bookId)

        if (result.success) {
          // 更新书籍信息
          this.novelInfo = {
            novelId: result.data.novelId,
            novelName: result.data.novelName,
            novelAuthor: result.data.author,
            novelDesc: result.data.description,
            coverUrl: result.data.coverUrl || '',
            type: 'web',
            totalChapters: result.data.totalChapters || 0
          }

          this.$message.success(`解析成功！书名：${result.data.novelName}，作者：${result.data.author}`)

          // 显示解析结果
          this.$alert(
            `<div style="text-align: left;">
              <p><strong>书名：</strong>${result.data.novelName}</p>
              <p><strong>作者：</strong>${result.data.author}</p>
              <p><strong>简介：</strong>${result.data.description}</p>
              <p><strong>解析时间：</strong>${new Date(result.data.parseTime).toLocaleString()}</p>
            </div>`,
            '书籍信息解析结果',
            {
              dangerouslyUseHTMLString: true,
              confirmButtonText: '确定'
            }
          )
        } else {
          this.$message.error('解析失败：' + result.message)
        }
      } catch (error) {
        console.error('解析书籍信息失败:', error)
        this.$message.error('解析失败：' + error.message)
      } finally {
        this.isParsing = false
      }
    },



    // 选择保存路径
    selectSavePath() {
      // 在实际应用中，这里应该调用文件选择器
      this.$prompt('请输入保存路径', '选择保存路径', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: this.form.savePath
      }).then(({ value }) => {
        this.form.savePath = value
      }).catch(() => {})
    },



    // 下载控制 - 自动解析书籍信息后下载
    async startDownload() {
      try {
        await this.$refs.downloadForm.validate()

        if (!this.$store.getters['user/isActivated']) {
          this.$message.error('请先激活软件')
          this.$router.push('/activation')
          return
        }

        if (!this.form.novelUrl.trim()) {
          this.$message.error('请输入小说链接或书籍ID')
          return
        }

        // 提取书籍ID
        const bookId = this.$api.novel.extractBookId(this.form.novelUrl)
        if (!bookId) {
          this.$message.error('无效的小说链接，请检查链接格式')
          return
        }

        // 设置解析状态
        this.isParsing = true
        this.$message.info('正在解析书籍信息，请稍候...')

        // 自动解析书籍信息
        let bookInfo = null
        try {
          const parseResult = await this.$api.novel.parseBookInfo(bookId)
          if (parseResult.success) {
            bookInfo = parseResult.data
            this.$message.success(`解析成功：《${bookInfo.novelName}》 - ${bookInfo.author}`)

            // 更新本地书籍信息
            this.novelInfo = {
              novelId: bookInfo.novelId,
              novelName: bookInfo.novelName,
              novelAuthor: bookInfo.author,
              novelDesc: bookInfo.description,
              coverUrl: bookInfo.coverUrl || '',
              type: 'web',
              totalChapters: bookInfo.totalChapters || 0
            }
          } else {
            this.$message.warning('解析书籍信息失败，使用默认信息：' + parseResult.message)
            // 使用默认信息
            bookInfo = {
              novelId: bookId,
              novelName: `小说_${bookId}`,
              author: '未知作者',
              description: '暂无简介'
            }
          }
        } catch (parseError) {
          console.error('解析书籍信息出错:', parseError)
          this.$message.warning('解析书籍信息失败，使用默认信息')
          // 使用默认信息
          bookInfo = {
            novelId: bookId,
            novelName: `小说_${bookId}`,
            author: '未知作者',
            description: '暂无简介'
          }
        } finally {
          // 清除解析状态
          this.isParsing = false
        }

        // 设置当前下载信息（使用解析到的信息）
        await this.setCurrentDownload({
          platform: 'web',
          type: 'web',
          novelId: bookInfo.novelId,
          novelName: bookInfo.novelName,
          novelAuthor: bookInfo.author,
          novelDesc: bookInfo.description,
          novelUrl: this.form.novelUrl,
          coverUrl: bookInfo.coverUrl || '',
          totalChapters: bookInfo.totalChapters || 0,
          novelChapterIds: [], // 空数组
          novelChapterTitles: [], // 空数组
          format: 'txt' // 固定使用txt格式
        })

        // 开始下载
        await this.startDownloadAction()

        this.$message.success(`开始下载《${bookInfo.novelName}》`)
      } catch (error) {
        this.$message.error('启动下载失败：' + error.message)
        this.isParsing = false
      }
    },

    stopDownload() {
      this.stopDownloadAction()
      this.$message.info('下载已停止')
    },



    formatTime(seconds) {
      if (!seconds) return '0秒'
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = seconds % 60

      if (hours > 0) {
        return `${hours}小时${minutes}分钟`
      } else if (minutes > 0) {
        return `${minutes}分钟${secs}秒`
      } else {
        return `${secs}秒`
      }
    },

    // 下载完成后的文件下载
    async downloadCompletedFile() {
      try {
        if (!this.downloadStatus.fileUrl) {
          this.$message.error('下载链接不存在')
          return
        }

        // 确保文件名安全
        const safeNovelName = this.novelInfo.novelName.replace(/[<>:"/\\|?*]/g, '_')
        const fileName = `${safeNovelName}.txt`

        this.$message.info('正在准备下载文件...')

        const success = await this.downloadFileAction({
          fileUrl: this.downloadStatus.fileUrl,
          fileName: fileName,
          formatType: 'txt'
        })

        if (success) {
          this.$message.success('文件下载已开始，请检查浏览器下载文件夹')
        } else {
          this.$message.error('文件下载失败，请尝试复制链接手动下载')
        }
      } catch (error) {
        console.error('下载文件错误:', error)
        this.$message.error('文件下载失败：' + error.message)
      }
    },

    // 刷新页面
    refreshPage() {
      this.$confirm('确定要刷新页面吗？当前下载进度将会丢失。', '确认刷新', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        window.location.reload()
      }).catch(() => {
        // 用户取消刷新
      })
    },

    // 重试下载
    async retryDownload() {
      try {
        // 重置下载状态
        this.resetDownload()
        this.$message.info('正在重新开始下载...')
        await this.startDownload()
      } catch (error) {
        this.$message.error('重试下载失败：' + error.message)
      }
    },

    // 检查用户额度
    async checkQuota() {
      try {
        const result = await this.checkUserQuotaAction()
        if (result.success) {
          this.$message.success('额度检查成功')
          return result.quota
        } else {
          this.$message.error('额度检查失败：' + result.error)
          return null
        }
      } catch (error) {
        this.$message.error('额度检查失败：' + error.message)
        return null
      }
    },

    // 检查网络连接
    async checkNetworkConnection() {
      try {
        // 尝试访问番茄小说官方网站来检测网络连接
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 5000) // 5秒超时

        const response = await fetch('https://fanqienovel.com', {
          method: 'HEAD', // 只获取头部信息，减少流量
          signal: controller.signal,
          mode: 'no-cors' // 避免CORS问题
        })

        clearTimeout(timeoutId)
        return true // 如果请求没有抛出异常，说明网络正常
      } catch (error) {
        console.warn('网络连接检测失败:', error)
        // 如果是超时错误，说明网络可能有问题
        return error.name !== 'AbortError'
      }
    },

    // 验证书籍ID格式
    validateBookId(bookId) {
      if (!bookId) return false

      // 检查是否为纯数字且长度合理
      if (!/^\d+$/.test(bookId)) return false

      // 检查长度（通常番茄小说ID长度在7-20位之间）
      if (bookId.length < 7 || bookId.length > 20) return false

      return true
    },

    // 显示解析进度
    showParsingProgress() {
      let progress = 0
      const progressInterval = setInterval(() => {
        progress += Math.random() * 15 + 5
        if (progress >= 90) {
          progress = 90
          clearInterval(progressInterval)
        }
        // 这里可以更新一个进度条，如果需要的话
      }, 500)

      return progressInterval
    }
  }
}
</script>

<style lang="scss" scoped>
.fanqie-download {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  // 页面头部
  .page-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 20px 0;
    margin-bottom: 30px;

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .logo-section {
        .logo {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          i {
            font-size: 32px;
            color: #667eea;
            margin-right: 12px;
          }

          h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }
        }

        .subtitle {
          margin: 0;
          color: #666;
          font-size: 14px;
        }
      }

      .user-section {
        .user-info {
          display: flex;
          align-items: center;
          gap: 15px;

          .remaining {
            color: #666;
            font-size: 14px;
          }

          .refresh-btn {
            color: #667eea;
            font-size: 12px;
            padding: 0;
            margin-left: 5px;

            &:hover {
              color: #5a6fd8;
            }

            &.is-loading {
              color: #ccc;
            }
          }
        }
      }
    }
  }

  // 主要内容
  .main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  // 下载卡片
  .download-card {
    margin-bottom: 30px;
    border-radius: 16px;
    border: none;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

    .card-header {
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      color: #303133;

      i {
        margin-right: 8px;
        color: #667eea;
        font-size: 20px;
      }
    }

    // 代理服务器状态提示
    .proxy-status-alert {
      margin-bottom: 20px;

      .el-alert {
        border-radius: 12px;
        border: none;
        background: linear-gradient(135deg, rgba(230, 162, 60, 0.1), rgba(230, 162, 60, 0.05));
        border-left: 4px solid #e6a23c;

        .proxy-actions {
          margin-top: 10px;
          display: flex;
          gap: 15px;

          .el-button--text {
            color: #e6a23c;
            font-size: 12px;
            padding: 0;

            &:hover {
              color: #cf9236;
            }

            i {
              margin-right: 4px;
            }
          }
        }
      }
    }
  }

  // 书籍信息卡片
  .book-info-card {
    margin-bottom: 30px;
    border-radius: 16px;
    border: none;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));

    .card-header {
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      color: #303133;

      i {
        margin-right: 8px;
        color: #667eea;
        font-size: 20px;
      }
    }

    .book-info-content {
      .book-basic-info {
        margin-bottom: 20px;

        .book-title {
          margin: 0 0 10px 0;
          font-size: 20px;
          font-weight: 600;
          color: #303133;
          line-height: 1.4;
        }

        .book-author {
          margin: 0 0 10px 0;
          font-size: 14px;
          color: #667eea;
          font-weight: 500;
        }

        .book-desc {
          margin: 0;
          font-size: 14px;
          color: #666;
          line-height: 1.6;
          max-height: 60px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
        }
      }

      .book-meta {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
      }
    }

    .form-row {
      margin-bottom: 20px;

      &.options-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 20px;

        .option-group {
          display: flex;
          align-items: center;
          gap: 15px;

          .option-label {
            font-weight: 500;
            color: #303133;
            margin-right: 10px;
          }
        }
      }
    }

    .url-input {
      .el-input {
        .el-input-group__prepend {
          background: #667eea;
          color: white;
          border-color: #667eea;
        }
      }
    }

    .test-links {
      margin-top: 15px;
      display: flex;
      align-items: center;
      gap: 10px;
      flex-wrap: wrap;

      .test-label {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }

      .test-link-tag {
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          transform: scale(1.05);
          box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }
      }
    }
  }

  // 章节信息展示区域
  .chapter-info-section {
    margin-bottom: 30px;

    .chapter-info-card {
      border-radius: 16px;
      border: none;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

      .card-header {
        display: flex;
        align-items: center;
        font-size: 18px;
        font-weight: 600;
        color: #303133;

        i {
          margin-right: 8px;
          color: #667eea;
          font-size: 20px;
        }
      }

      .chapter-summary {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 25px;

        .summary-item {
          display: flex;
          align-items: center;
          padding: 15px;
          background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
          border-radius: 12px;
          border: 1px solid rgba(102, 126, 234, 0.2);

          .summary-label {
            font-size: 14px;
            color: #666;
            margin-right: 8px;
          }

          .summary-value {
            font-size: 16px;
            font-weight: 600;
            color: #303133;

            &.highlight {
              color: #667eea;
            }
          }
        }
      }

      .download-notice {
        .el-alert {
          border-radius: 12px;
          border: none;
          background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));

          p {
            margin: 5px 0;
            line-height: 1.6;

            strong {
              color: #667eea;
            }
          }
        }
      }
    }
  }

  // 下载操作区域
  .download-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

    .download-summary {
      text-align: center;
      margin-bottom: 25px;
      padding: 20px;
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
      border-radius: 12px;
      border: 1px solid rgba(102, 126, 234, 0.2);

      .summary-info {
        .summary-text {
          font-size: 16px;
          color: #303133;
          margin-bottom: 15px;

          strong {
            color: #667eea;
            font-weight: 600;
          }
        }


      }
    }

    .download-actions {
      display: flex;
      gap: 15px;
      justify-content: center;
      align-items: center;
      margin-top: 25px;

      .parse-btn {
        min-width: 140px;
        height: 50px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 25px;
        background: linear-gradient(135deg, #909399, #606266);
        border: none;
        box-shadow: 0 4px 16px rgba(144, 147, 153, 0.3);
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(144, 147, 153, 0.4);
        }

        &:active {
          transform: translateY(0);
        }

        &.is-loading {
          background: #ccc;
        }
      }

      .download-btn {
        min-width: 160px;
        height: 50px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 25px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        &:active {
          transform: translateY(0);
        }

        &.is-loading {
          background: #ccc;
        }
      }

      .stop-btn {
        min-width: 120px;
        height: 50px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 25px;
        background: linear-gradient(135deg, #f56c6c, #e85a5a);
        border: none;
        box-shadow: 0 4px 16px rgba(245, 108, 108, 0.3);
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(245, 108, 108, 0.4);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }

    .download-progress {
      margin-top: 25px;

      .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .progress-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }

        .progress-percent {
          font-size: 18px;
          font-weight: 600;
          color: #667eea;
        }
      }

      .progress-details {
        display: flex;
        justify-content: space-between;
        margin-top: 8px;
        font-size: 12px;
        color: #999;
      }

      .download-complete, .download-failed {
        margin-top: 20px;

        .complete-alert, .failed-alert {
          border-radius: 12px;
          border: none;

          p {
            margin: 8px 0;
            font-size: 14px;
            line-height: 1.6;
          }

          .download-actions {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;

            .el-button {
              border-radius: 20px;
              font-size: 12px;
              padding: 8px 16px;
              transition: all 0.3s;

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
              }
            }
          }
        }

        .complete-alert {
          background: linear-gradient(135deg, rgba(103, 194, 58, 0.1), rgba(103, 194, 58, 0.05));
          border-left: 4px solid #67c23a;
        }

        .failed-alert {
          background: linear-gradient(135deg, rgba(245, 108, 108, 0.1), rgba(245, 108, 108, 0.05));
          border-left: 4px solid #f56c6c;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .fanqie-download {
    .page-header {
      .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;

        .logo-section .logo h1 {
          font-size: 24px;
        }
      }
    }

    .main-content {
      padding: 0 15px;
    }

    .novel-preview .preview-content {
      flex-direction: column;
      text-align: center;
      gap: 20px;

      .novel-details .novel-stats {
        justify-content: center;
      }
    }

    .chapter-info-section {
      .chapter-summary {
        grid-template-columns: 1fr !important;
        gap: 15px;
      }
    }

    .download-actions {
      flex-direction: column;
      gap: 12px;
      margin-top: 20px;

      .download-btn,
      .stop-btn {
        width: 100%;
        min-width: auto;
        height: 48px;
        font-size: 15px;
      }
    }
  }
}


</style>
